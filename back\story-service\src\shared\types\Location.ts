export enum LocationType {
  SETTLEMENT = 'settlement',
  RUINS = 'ruins',
  BUNKER = 'bunker',
  FACTORY = 'factory',
  MILITARY_BASE = 'military_base',
  RESEARCH_FACILITY = 'research_facility',
  WASTELAND = 'wasteland',
  RADIATION_ZONE = 'radiation_zone'
}

export interface Location {
  id: string
  name: string
  type: LocationType
  description: string
  position: {
    x: number
    y: number
  }
  size: {
    width: number
    height: number
  }
  npcs: string[]
  resources: Record<string, number>
  dangerLevel: number
  radiationLevel: number
  isDiscovered: boolean
  isAccessible: boolean
}
