import { Injectable, Logger } from '@nestjs/common'
import { AiService } from '../ai/ai.service'
import { Location } from '@shared/types/Location'
import { NPC } from '@shared/types/NPC'
import { Faction } from '@shared/types/Faction'
import { AIGenerationContext } from './types/world-generation.types'

/**
 * Помощник для интеграции с AI
 * Предоставляет методы для улучшения генерируемого контента с помощью AI
 */
@Injectable()
export class AIHelper {
  private readonly logger = new Logger(AIHelper.name)
  
  constructor(private readonly aiService: AiService) {}

  /**
   * Улучшает описание локации с помощью AI
   */
  async enhanceLocationDescription(location: Location, context: AIGenerationContext): Promise<string> {
    try {
      const prompt = this.buildLocationPrompt(location, context)
      
      const response = await this.aiService.generateStory({
        prompt,
        context: {
          location_type: location.type,
          danger_level: location.dangerLevel,
          has_water: location.hasWater,
          has_electricity: location.hasElectricity
        }
      })
      
      if (response.success && response.content) {
        return response.content.trim()
      }
      
      return location.description
    } catch (error) {
      this.logger.warn(`Failed to enhance location description: ${error.message}`)
      return location.description
    }
  }

  /**
   * Генерирует историю фракции с помощью AI
   */
  async generateFactionHistory(faction: Faction, context: AIGenerationContext): Promise<string> {
    try {
      const prompt = this.buildFactionHistoryPrompt(faction, context)
      
      const response = await this.aiService.generateStory({
        prompt,
        context: {
          faction_name: faction.name,
          faction_ideology: faction.ideology,
          faction_goals: faction.goals,
          member_count: faction.memberCount
        }
      })
      
      if (response.success && response.content) {
        return response.content.trim()
      }
      
      return faction.history
    } catch (error) {
      this.logger.warn(`Failed to generate faction history: ${error.message}`)
      return faction.history
    }
  }

  /**
   * Генерирует диалоги для NPC с помощью AI
   */
  async generateNPCDialogues(npc: NPC, location: Location, faction?: Faction): Promise<any[]> {
    try {
      const prompt = this.buildNPCDialoguePrompt(npc, location, faction)
      
      const response = await this.aiService.generateDialogue(
        npc.name,
        npc.type,
        this.buildPersonalityString(npc),
        {
          location_type: location.type,
          faction_name: faction?.name,
          npc_level: npc.level
        }
      )
      
      if (response.success && response.content) {
        return this.parseDialogueResponse(response.content)
      }
      
      return []
    } catch (error) {
      this.logger.warn(`Failed to generate NPC dialogues: ${error.message}`)
      return []
    }
  }

  /**
   * Генерирует квесты для локации с помощью AI
   */
  async generateLocationQuests(location: Location, context: AIGenerationContext): Promise<string[]> {
    try {
      const prompt = this.buildQuestPrompt(location, context)
      
      const response = await this.aiService.generateQuest(
        'exploration',
        5, // базовый уровень
        location.name,
        {
          location_type: location.type,
          danger_level: location.dangerLevel,
          theme: context.worldTheme
        }
      )
      
      if (response.success && response.content) {
        return this.parseQuestResponse(response.content)
      }
      
      return []
    } catch (error) {
      this.logger.warn(`Failed to generate location quests: ${error.message}`)
      return []
    }
  }

  /**
   * Генерирует события для мира с помощью AI
   */
  async generateWorldEvents(context: AIGenerationContext): Promise<any[]> {
    try {
      const prompt = this.buildWorldEventsPrompt(context)
      
      const response = await this.aiService.generateEvent('world_event', {
        world_theme: context.worldTheme,
        time_after_apocalypse: context.timeAfterApocalypse,
        main_conflicts: context.mainConflicts
      })
      
      if (response.success && response.content) {
        return this.parseEventsResponse(response.content)
      }
      
      return []
    } catch (error) {
      this.logger.warn(`Failed to generate world events: ${error.message}`)
      return []
    }
  }

  /**
   * Строит промпт для улучшения описания локации
   */
  private buildLocationPrompt(location: Location, context: AIGenerationContext): string {
    return `
Создай детальное описание локации в постапокалиптическом мире.

Контекст мира:
- Тема: ${context.worldTheme}
- Лет после апокалипсиса: ${context.timeAfterApocalypse}
- Основные конфликты: ${context.mainConflicts.join(', ')}
- Уровень технологий: ${context.technologyLevel}

Информация о локации:
- Название: ${location.name}
- Тип: ${location.type}
- Уровень опасности: ${location.dangerLevel}/100
- Есть вода: ${location.hasWater ? 'да' : 'нет'}
- Есть электричество: ${location.hasElectricity ? 'да' : 'нет'}
- Уровень радиации: ${location.radiationLevel}

Создай атмосферное описание этой локации длиной 2-3 предложения, которое передает её состояние, историю и текущую ситуацию.
    `.trim()
  }

  /**
   * Строит промпт для генерации истории фракции
   */
  private buildFactionHistoryPrompt(faction: Faction, context: AIGenerationContext): string {
    return `
Создай историю фракции в постапокалиптическом мире.

Контекст мира:
- Тема: ${context.worldTheme}
- Лет после апокалипсиса: ${context.timeAfterApocalypse}
- Основные конфликты: ${context.mainConflicts.join(', ')}

Информация о фракции:
- Название: ${faction.name}
- Идеология: ${faction.ideology}
- Цели: ${faction.goals.join(', ')}
- Количество членов: ${faction.memberCount}
- Тип правления: ${faction.governmentType}

Создай краткую историю фракции (3-4 предложения), объясняющую как она образовалась, через что прошла и какова её текущая ситуация.
    `.trim()
  }

  /**
   * Строит промпт для генерации диалогов NPC
   */
  private buildNPCDialoguePrompt(npc: NPC, location: Location, faction?: Faction): string {
    return `
Создай диалоги для NPC в постапокалиптическом мире.

Информация об NPC:
- Имя: ${npc.name}
- Тип: ${npc.type}
- Уровень: ${npc.level}
- Возраст: ${npc.appearance.age}
- Фракция: ${faction?.name || 'независимый'}

Локация: ${location.name} (${location.type})

Создай 3-5 коротких фраз, которые этот NPC может сказать игроку при встрече. Учти его тип, локацию и фракцию.
    `.trim()
  }

  /**
   * Строит промпт для генерации квестов
   */
  private buildQuestPrompt(location: Location, context: AIGenerationContext): string {
    return `
Создай идеи для квестов в локации постапокалиптического мира.

Контекст мира:
- Тема: ${context.worldTheme}
- Основные конфликты: ${context.mainConflicts.join(', ')}

Локация:
- Название: ${location.name}
- Тип: ${location.type}
- Уровень опасности: ${location.dangerLevel}/100

Предложи 2-3 идеи квестов для этой локации. Каждая идея должна быть в одном предложении.
    `.trim()
  }

  /**
   * Строит промпт для генерации мировых событий
   */
  private buildWorldEventsPrompt(context: AIGenerationContext): string {
    return `
Создай идеи для случайных событий в постапокалиптическом мире.

Контекст мира:
- Тема: ${context.worldTheme}
- Лет после апокалипсиса: ${context.timeAfterApocalypse}
- Основные конфликты: ${context.mainConflicts.join(', ')}
- Ключевые фракции: ${context.keyFactions.join(', ')}

Предложи 3-5 идей для случайных событий, которые могут произойти в этом мире. Каждое событие опиши в 1-2 предложениях.
    `.trim()
  }

  /**
   * Строит строку описания личности NPC
   */
  private buildPersonalityString(npc: NPC): string {
    const traits = []
    
    if (npc.personality.aggression > 70) traits.push('агрессивный')
    else if (npc.personality.aggression < 30) traits.push('миролюбивый')
    
    if (npc.personality.friendliness > 70) traits.push('дружелюбный')
    else if (npc.personality.friendliness < 30) traits.push('замкнутый')
    
    if (npc.personality.intelligence > 70) traits.push('умный')
    else if (npc.personality.intelligence < 30) traits.push('простоватый')
    
    if (npc.personality.courage > 70) traits.push('храбрый')
    else if (npc.personality.courage < 30) traits.push('трусливый')
    
    return traits.length > 0 ? traits.join(', ') : 'обычный'
  }

  /**
   * Парсит ответ AI с диалогами
   */
  private parseDialogueResponse(content: string): any[] {
    const dialogues = []
    const lines = content.split('\n').filter(line => line.trim())
    
    lines.forEach((line, index) => {
      const cleanLine = line.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, '').trim()
      if (cleanLine) {
        dialogues.push({
          id: `dialogue_${index}`,
          text: cleanLine,
          responses: [
            {
              id: `response_${index}_1`,
              text: 'Понятно.',
              action: 'leave'
            }
          ]
        })
      }
    })
    
    return dialogues
  }

  /**
   * Парсит ответ AI с квестами
   */
  private parseQuestResponse(content: string): string[] {
    const quests = []
    const lines = content.split('\n').filter(line => line.trim())
    
    lines.forEach(line => {
      const cleanLine = line.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, '').trim()
      if (cleanLine) {
        quests.push(cleanLine)
      }
    })
    
    return quests
  }

  /**
   * Парсит ответ AI с событиями
   */
  private parseEventsResponse(content: string): any[] {
    const events = []
    const lines = content.split('\n').filter(line => line.trim())
    
    lines.forEach((line, index) => {
      const cleanLine = line.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, '').trim()
      if (cleanLine) {
        events.push({
          id: `event_${index}`,
          title: `Событие ${index + 1}`,
          description: cleanLine,
          type: 'random',
          probability: 0.1
        })
      }
    })
    
    return events
  }

  /**
   * Создает контекст для AI генерации на основе настроек мира
   */
  createGenerationContext(worldSettings: any, userId: string): AIGenerationContext {
    return {
      worldTheme: 'постапокалиптический мир после ядерной войны',
      timeAfterApocalypse: 10 + Math.floor(Math.random() * 40), // 10-50 лет
      mainConflicts: [
        'борьба за ресурсы',
        'территориальные споры',
        'идеологические разногласия'
      ],
      keyFactions: ['Выжившие', 'Военные', 'Торговцы'],
      environmentalHazards: [
        'радиация',
        'мутанты',
        'нехватка воды',
        'экстремальная погода'
      ],
      technologyLevel: 'смешанный: от примитивного до довоенного',
      playerStartingLocation: 'небольшое поселение выживших'
    }
  }

  /**
   * Проверяет доступность AI сервиса
   */
  async isAIAvailable(): Promise<boolean> {
    try {
      const response = await this.aiService.generateStory({
        prompt: 'test',
        context: {}
      })
      return response.success
    } catch (error) {
      this.logger.warn('AI service is not available')
      return false
    }
  }
}
