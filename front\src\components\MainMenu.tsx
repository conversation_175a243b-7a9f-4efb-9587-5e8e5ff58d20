import React, { useState } from 'react'
import { Play, Settings, X, Home } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import WorldList from './WorldList'
import SettingsPanel from './SettingsPanel'
import styles from './MainMenu.module.css'

interface MainMenuProps {
  onStartGame?: (worldId: string) => void
}

const MainMenu: React.FC<MainMenuProps> = ({ onStartGame }) => {
  const [activePanel, setActivePanel] = useState<'main' | 'worlds' | 'settings'>('main')
  const navigate = useNavigate()

  const handlePlayClick = () => {
    setActivePanel('worlds')
  }

  const handleSettingsClick = () => {
    setActivePanel('settings')
  }

  const handleBackToMain = () => {
    setActivePanel('main')
  }

  const handleWorldSelect = (worldId: string) => {
    onStartGame?.(worldId)
  }

  const handleExitToHome = () => {
    navigate('/')
  }

  return (
    <div className={styles.mainMenu}>
      {/* Dark overlay for better text readability */}
      <div className={styles.overlay} />

      {/* Main content */}
      <div className={styles.content}>

        {/* Main Menu */}
        {activePanel === 'main' && (
          <div className={styles.mainPanel}>
            {/* Game Title */}
            <div className={styles.titleSection}>
              <h1 className={styles.title}>
                ☢️ NuclearStory
              </h1>
              <p className={styles.subtitle}>
                Выживите в постапокалиптической пустоши в этой интерактивной истории, управляемой ИИ
              </p>
            </div>

            {/* Menu Buttons */}
            <div className={styles.menuButtons}>
              <button
                onClick={handlePlayClick}
                className={`${styles.menuButton} ${styles.playButton}`}
              >
                <Play />
                Играть
              </button>

              <button
                onClick={handleSettingsClick}
                className={`${styles.menuButton} ${styles.settingsButton}`}
              >
                <Settings />
                Настройки
              </button>

              <button
                onClick={handleExitToHome}
                className={`${styles.menuButton} ${styles.exitButton}`}
              >
                <Home />
                Выйти
              </button>
            </div>
          </div>
        )}

        {/* World Selection Panel */}
        {activePanel === 'worlds' && (
          <div className={styles.panel}>
            <div className={styles.panelHeader}>
              <h2 className={styles.panelTitle}>Выбор мира</h2>
              <button
                onClick={handleBackToMain}
                className={styles.closeButton}
              >
                <X />
              </button>
            </div>
            <WorldList onWorldSelect={handleWorldSelect} />
          </div>
        )}

        {/* Settings Panel */}
        {activePanel === 'settings' && (
          <div className={`${styles.panel} ${styles.panelSmall}`}>
            <div className={styles.panelHeader}>
              <h2 className={styles.panelTitle}>Настройки</h2>
              <button
                onClick={handleBackToMain}
                className={styles.closeButton}
              >
                <X />
              </button>
            </div>
            <SettingsPanel />
          </div>
        )}
      </div>
    </div>
  )
}

export default MainMenu
