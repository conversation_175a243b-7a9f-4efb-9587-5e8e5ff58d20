export interface WorldSettings {
  seed: string
  worldSize: {
    width: number
    height: number
  }
  difficulty: 'easy' | 'normal' | 'hard' | 'nightmare'
  generationAlgorithm: 'perlin' | 'cellular' | 'voronoi' | 'hybrid'
  locationDensity: number
  npcDensity: number
  resourceAbundance: number
  dangerLevel: number
  globalRadiation: number
  radiationZones: boolean
  weatherSystem: boolean
  seasonalChanges: boolean
  dynamicEconomy: boolean
  factionWars: boolean
  timeScale: number
  dayDuration: number
}

export interface WorldMetadata {
  name: string
  description: string
  createdAt: Date
  lastUpdatedAt: Date
  createdBy: string
  tags: string[]
  version: string
  isPublic: boolean
}

export interface WorldState {
  currentTime: number
  daysPassed: number
  weather: {
    current: string
    temperature: number
  }
}

export interface World {
  id: string
  metadata: WorldMetadata
  settings: WorldSettings
  worldGrid: any[][]
  locations: Record<string, any>
  npcs: Record<string, any>
  factions: Record<string, any>
  state: WorldState
}
