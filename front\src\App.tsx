import { Routes, Route } from 'react-router-dom'
import { AuthProvider } from './store/authStore'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import SignupPage from './pages/SignupPage'
import MainMenuPage from './pages/MainMenuPage'

function App() {
  return (
    <AuthProvider>
      <div className="app">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="login" element={<LoginPage />} />
            <Route path="signup" element={<SignupPage />} />
          </Route>
          <Route path="/menu" element={<MainMenuPage />} />
        </Routes>
      </div>
    </AuthProvider>
  )
}

export default App
