import { Controller, Post, Body, Get, Param, Query } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger'
import { WorldGenerationService } from './world-generation.service'
import { CreateWorldDto, WorldGenerationStatusDto } from './dto/world-generation.dto'

/**
 * Контроллер для генерации мира
 * Предоставляет API endpoints для создания и управления мирами
 */
@ApiTags('World Generation')
@Controller('world-generation')
export class WorldGenerationController {
  constructor(private readonly worldGenerationService: WorldGenerationService) {}

  /**
   * Создает новый мир
   */
  @Post('create')
  @ApiOperation({ summary: 'Create a new world' })
  @ApiResponse({ status: 201, description: 'World creation started successfully' })
  @ApiResponse({ status: 400, description: 'Invalid world parameters' })
  @ApiBody({ type: CreateWorldDto })
  async createWorld(@Body() createWorldDto: CreateWorldDto) {
    return this.worldGenerationService.createWorld(createWorldDto)
  }

  /**
   * Получает статус генерации мира
   */
  @Get('status/:worldId')
  @ApiOperation({ summary: 'Get world generation status' })
  @ApiResponse({ status: 200, description: 'Generation status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'World not found' })
  async getGenerationStatus(@Param('worldId') worldId: string) {
    return this.worldGenerationService.getGenerationStatus(worldId)
  }

  /**
   * Получает сгенерированный мир
   */
  @Get('world/:worldId')
  @ApiOperation({ summary: 'Get generated world' })
  @ApiResponse({ status: 200, description: 'World retrieved successfully' })
  @ApiResponse({ status: 404, description: 'World not found' })
  async getWorld(@Param('worldId') worldId: string) {
    return this.worldGenerationService.getWorld(worldId)
  }

  /**
   * Получает список миров пользователя
   */
  @Get('user/:userId/worlds')
  @ApiOperation({ summary: 'Get user worlds' })
  @ApiResponse({ status: 200, description: 'User worlds retrieved successfully' })
  async getUserWorlds(@Param('userId') userId: string) {
    return this.worldGenerationService.getUserWorlds(userId)
  }

  /**
   * Удаляет мир
   */
  @Post('delete/:worldId')
  @ApiOperation({ summary: 'Delete a world' })
  @ApiResponse({ status: 200, description: 'World deleted successfully' })
  @ApiResponse({ status: 404, description: 'World not found' })
  async deleteWorld(@Param('worldId') worldId: string) {
    return this.worldGenerationService.deleteWorld(worldId)
  }

  /**
   * Получает шаблоны для генерации
   */
  @Get('templates')
  @ApiOperation({ summary: 'Get generation templates' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplates(@Query('type') type?: string) {
    return this.worldGenerationService.getTemplates(type)
  }

  /**
   * Получает предустановки мира
   */
  @Get('presets')
  @ApiOperation({ summary: 'Get world presets' })
  @ApiResponse({ status: 200, description: 'Presets retrieved successfully' })
  async getWorldPresets() {
    return this.worldGenerationService.getWorldPresets()
  }
}
