import { Module } from '@nestjs/common'
import { WorldGenerator } from './world.generator'
import { LocationGenerator } from './location.generator'
import { FactionGenerator } from './faction.generator'
import { NPCGenerator } from './npc.generator'
import { MapGenerator } from './map.generator'
import { AIHelper } from './ai.helper'
import { WorldGenerationController } from './world-generation.controller'
import { WorldGenerationService } from './world-generation.service'

/**
 * Модуль генерации мира
 * Объединяет все компоненты для создания игровых миров
 */
@Module({
  controllers: [WorldGenerationController],
  providers: [
    WorldGenerationService,
    WorldGenerator,
    LocationGenerator,
    FactionGenerator,
    NPCGenerator,
    MapGenerator,
    AIHelper
  ],
  exports: [
    WorldGenerationService,
    WorldGenerator
  ]
})
export class WorldGenerationModule {}
