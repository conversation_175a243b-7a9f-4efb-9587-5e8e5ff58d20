import { WorldSettings, World } from '@shared/types/World'
import { Location } from '@shared/types/Location'
import { NPC } from '@shared/types/NPC'
import { Faction } from '@shared/types/Faction'
import { MapCell } from '@shared/types/MapCell'

/**
 * Параметры генерации мира
 */
export interface WorldGenerationParams {
  // Основные параметры
  seed: string
  worldSize: {
    width: number
    height: number
  }
  
  // Настройки генерации
  settings: WorldSettings
  
  // Пользователь для которого создается мир
  userId: string
  
  // Дополнительные параметры
  generateWithAI: boolean
  aiPromptContext?: string
}

/**
 * Результат генерации мира
 */
export interface WorldGenerationResult {
  success: boolean
  world?: World
  error?: string
  generationTime: number // миллисекунды
  stats: {
    locationsGenerated: number
    npcsGenerated: number
    factionsGenerated: number
    cellsGenerated: number
  }
}

/**
 * Параметры генерации локаций
 */
export interface LocationGenerationParams {
  worldSeed: string
  worldSize: { width: number; height: number }
  locationDensity: number // 0-100
  dangerLevel: number // 0-100
  existingLocations: Location[]
  biomeType?: string
}

/**
 * Параметры генерации фракций
 */
export interface FactionGenerationParams {
  worldSeed: string
  worldSettings: WorldSettings
  existingFactions: Faction[]
  maxFactions: number
  generateWithAI: boolean
}

/**
 * Параметры генерации NPC
 */
export interface NPCGenerationParams {
  worldSeed: string
  locations: Location[]
  factions: Faction[]
  npcDensity: number // 0-100
  generateWithAI: boolean
}

/**
 * Параметры генерации карты
 */
export interface MapGenerationParams {
  seed: string
  width: number
  height: number
  algorithm: 'perlin' | 'cellular' | 'voronoi' | 'hybrid'
  settings: {
    landRatio: number // 0-1, соотношение суши к воде
    mountainDensity: number // 0-1
    forestDensity: number // 0-1
    radiationZones: boolean
    globalRadiation: number
  }
}

/**
 * Контекст для AI генерации
 */
export interface AIGenerationContext {
  worldTheme: string
  timeAfterApocalypse: number // лет
  mainConflicts: string[]
  keyFactions: string[]
  environmentalHazards: string[]
  technologyLevel: string
  playerStartingLocation?: string
}

/**
 * Прогресс генерации мира
 */
export interface GenerationProgress {
  stage: 'initializing' | 'generating_map' | 'generating_locations' | 'generating_factions' | 'generating_npcs' | 'finalizing'
  progress: number // 0-100
  currentTask: string
  estimatedTimeRemaining?: number // секунды
}

/**
 * Настройки генератора
 */
export interface GeneratorConfig {
  // Ограничения производительности
  maxGenerationTime: number // миллисекунды
  maxLocationsPerWorld: number
  maxNPCsPerWorld: number
  maxFactionsPerWorld: number
  
  // AI настройки
  aiTimeout: number // миллисекунды
  aiRetryAttempts: number
  
  // Кэширование
  enableCaching: boolean
  cacheExpiration: number // секунды
}

/**
 * Шаблон для генерации
 */
export interface GenerationTemplate {
  id: string
  name: string
  description: string
  type: 'location' | 'faction' | 'npc' | 'quest'
  
  // Условия применения
  conditions: {
    biome?: string[]
    dangerLevel?: { min: number; max: number }
    factionPresence?: string[]
    playerLevel?: { min: number; max: number }
  }
  
  // Параметры генерации
  parameters: Record<string, any>
  
  // AI промпт (если используется)
  aiPrompt?: string
}

/**
 * Биом мира
 */
export interface WorldBiome {
  id: string
  name: string
  description: string
  
  // Характеристики
  temperature: { min: number; max: number }
  humidity: { min: number; max: number }
  radiation: { min: number; max: number }
  
  // Вероятности генерации
  locationTypes: Record<string, number> // тип локации -> вероятность
  resourceTypes: Record<string, number> // тип ресурса -> изобилие
  hazardTypes: Record<string, number> // тип опасности -> вероятность
  
  // Визуальные характеристики
  terrainTextures: string[]
  ambientSounds: string[]
  weatherPatterns: string[]
}

/**
 * Зона мира
 */
export interface WorldZone {
  id: string
  name: string
  bounds: {
    x: number
    y: number
    width: number
    height: number
  }
  
  // Характеристики зоны
  biome: string
  dangerLevel: number
  controllingFaction?: string
  
  // Особенности
  specialFeatures: string[]
  restrictions: string[]
  
  // Связи с другими зонами
  connectedZones: string[]
  travelDifficulty: Record<string, number> // зона -> сложность перехода
}
