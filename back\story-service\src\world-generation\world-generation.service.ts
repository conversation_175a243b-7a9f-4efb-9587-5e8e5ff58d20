import { Injectable, Logger, NotFoundException } from '@nestjs/common'
import { WorldGenerator } from './world.generator'
import { AIHelper } from './ai.helper'
import { CreateWorldDto, WorldGenerationStatusDto, WorldPresetDto } from './dto/world-generation.dto'
import { WorldGenerationParams, WorldGenerationResult, GenerationProgress } from './types/world-generation.types'
import { World, WorldSettings } from '@shared/types/World'

/**
 * Сервис для управления генерацией мира
 * Координирует процесс создания миров и управляет их состоянием
 */
@Injectable()
export class WorldGenerationService {
  private readonly logger = new Logger(WorldGenerationService.name)
  
  // Хранилище для статусов генерации (в продакшене должно быть в Redis/БД)
  private generationStatuses = new Map<string, WorldGenerationStatusDto>()
  
  // Хранилище сгенерированных миров (в продакшене должно быть в БД)
  private generatedWorlds = new Map<string, World>()

  constructor(
    private readonly worldGenerator: WorldGenerator,
    private readonly aiHelper: AIHelper
  ) {}

  /**
   * Создает новый мир
   */
  async createWorld(createWorldDto: CreateWorldDto): Promise<{ worldId: string; status: string }> {
    this.logger.log(`Creating world for user ${createWorldDto.userId} with seed ${createWorldDto.seed}`)
    
    // Генерируем ID мира
    const worldId = this.generateWorldId(createWorldDto.seed, createWorldDto.userId)
    
    // Создаем статус генерации
    const status: WorldGenerationStatusDto = {
      worldId,
      status: 'pending',
      progress: 0,
      currentStage: 'initializing',
      currentTask: 'Подготовка к генерации мира...',
      startedAt: new Date()
    }
    
    this.generationStatuses.set(worldId, status)
    
    // Запускаем генерацию асинхронно
    this.startWorldGeneration(worldId, createWorldDto).catch(error => {
      this.logger.error(`World generation failed for ${worldId}:`, error)
      this.updateGenerationStatus(worldId, {
        status: 'failed',
        error: error.message,
        completedAt: new Date()
      })
    })
    
    return {
      worldId,
      status: 'started'
    }
  }

  /**
   * Получает статус генерации мира
   */
  async getGenerationStatus(worldId: string): Promise<WorldGenerationStatusDto> {
    const status = this.generationStatuses.get(worldId)
    
    if (!status) {
      throw new NotFoundException(`World generation status not found for ID: ${worldId}`)
    }
    
    return status
  }

  /**
   * Получает сгенерированный мир
   */
  async getWorld(worldId: string): Promise<World> {
    const world = this.generatedWorlds.get(worldId)
    
    if (!world) {
      throw new NotFoundException(`World not found for ID: ${worldId}`)
    }
    
    return world
  }

  /**
   * Получает список миров пользователя
   */
  async getUserWorlds(userId: string): Promise<{ worldId: string; world: World; status: WorldGenerationStatusDto }[]> {
    const userWorlds = []
    
    // Ищем миры пользователя
    for (const [worldId, world] of this.generatedWorlds.entries()) {
      if (world.metadata.createdBy === userId) {
        const status = this.generationStatuses.get(worldId)
        userWorlds.push({
          worldId,
          world,
          status: status || {
            worldId,
            status: 'completed',
            progress: 100,
            currentStage: 'completed',
            currentTask: 'Генерация завершена',
            startedAt: world.metadata.createdAt,
            completedAt: world.metadata.createdAt
          } as WorldGenerationStatusDto
        })
      }
    }
    
    return userWorlds
  }

  /**
   * Удаляет мир
   */
  async deleteWorld(worldId: string): Promise<{ success: boolean }> {
    const world = this.generatedWorlds.get(worldId)
    
    if (!world) {
      throw new NotFoundException(`World not found for ID: ${worldId}`)
    }
    
    // Удаляем мир и статус
    this.generatedWorlds.delete(worldId)
    this.generationStatuses.delete(worldId)
    
    this.logger.log(`World ${worldId} deleted successfully`)
    
    return { success: true }
  }

  /**
   * Получает шаблоны для генерации
   */
  async getTemplates(type?: string): Promise<any[]> {
    // В продакшене шаблоны должны храниться в БД
    const templates = [
      {
        id: 'military_base',
        name: 'Военная база',
        description: 'Шаблон для генерации военных объектов',
        type: 'location',
        conditions: {
          biome: ['wasteland', 'urban_ruins'],
          dangerLevel: { min: 60, max: 100 }
        },
        parameters: {
          hasWeapons: true,
          securityLevel: 'high',
          populationRange: [10, 30]
        }
      },
      {
        id: 'peaceful_settlement',
        name: 'Мирное поселение',
        description: 'Шаблон для генерации безопасных поселений',
        type: 'location',
        conditions: {
          biome: ['plains', 'forest'],
          dangerLevel: { min: 0, max: 30 }
        },
        parameters: {
          hasTraders: true,
          securityLevel: 'low',
          populationRange: [20, 80]
        }
      }
    ]
    
    if (type) {
      return templates.filter(template => template.type === type)
    }
    
    return templates
  }

  /**
   * Получает предустановки мира
   */
  async getWorldPresets(): Promise<WorldPresetDto[]> {
    return [
      {
        id: 'classic_wasteland',
        name: 'Классическая пустошь',
        description: 'Стандартный постапокалиптический мир с умеренной сложностью',
        settings: {
          difficulty: 'normal',
          locationDensity: 50,
          npcDensity: 40,
          dangerLevel: 45,
          globalRadiation: 25,
          radiationZones: true,
          weatherSystem: true,
          dynamicEconomy: true,
          factionWars: true
        },
        tags: ['beginner-friendly', 'balanced', 'exploration'],
        isRecommended: true
      },
      {
        id: 'harsh_survival',
        name: 'Суровое выживание',
        description: 'Сложный мир для опытных игроков с высоким уровнем опасности',
        settings: {
          difficulty: 'hard',
          locationDensity: 30,
          npcDensity: 60,
          dangerLevel: 80,
          globalRadiation: 50,
          radiationZones: true,
          weatherSystem: true,
          dynamicEconomy: false,
          factionWars: true
        },
        tags: ['challenging', 'survival', 'combat'],
        isRecommended: false
      },
      {
        id: 'peaceful_exploration',
        name: 'Мирное исследование',
        description: 'Спокойный мир для исследования без излишней агрессии',
        settings: {
          difficulty: 'easy',
          locationDensity: 70,
          npcDensity: 30,
          dangerLevel: 20,
          globalRadiation: 10,
          radiationZones: false,
          weatherSystem: true,
          dynamicEconomy: true,
          factionWars: false
        },
        tags: ['peaceful', 'exploration', 'story-focused'],
        isRecommended: true
      }
    ]
  }

  /**
   * Запускает генерацию мира
   */
  private async startWorldGeneration(worldId: string, createWorldDto: CreateWorldDto): Promise<void> {
    this.updateGenerationStatus(worldId, {
      status: 'in_progress',
      progress: 5,
      currentStage: 'preparing',
      currentTask: 'Подготовка параметров генерации...'
    })

    // Конвертируем DTO в параметры генерации
    const params = this.convertDtoToParams(createWorldDto)

    this.updateGenerationStatus(worldId, {
      progress: 10,
      currentStage: 'generating_map',
      currentTask: 'Генерация карты мира...'
    })

    // Запускаем генерацию
    const result = await this.worldGenerator.generateWorld(params)

    if (result.success && result.world) {
      // Сохраняем сгенерированный мир
      this.generatedWorlds.set(worldId, result.world)

      this.updateGenerationStatus(worldId, {
        status: 'completed',
        progress: 100,
        currentStage: 'completed',
        currentTask: 'Генерация мира завершена!',
        completedAt: new Date(),
        stats: result.stats
      })

      this.logger.log(`World generation completed for ${worldId}`)
    } else {
      throw new Error(result.error || 'Unknown generation error')
    }
  }

  /**
   * Конвертирует DTO в параметры генерации
   */
  private convertDtoToParams(dto: CreateWorldDto): WorldGenerationParams {
    const settings: WorldSettings = {
      seed: dto.seed,
      generationAlgorithm: dto.algorithm,
      worldSize: {
        width: dto.worldWidth,
        height: dto.worldHeight
      },
      difficulty: dto.difficulty,
      timeScale: dto.timeScale,
      dayDuration: dto.dayDuration,
      weatherSystem: dto.weatherSystem,
      seasonalChanges: dto.seasonalChanges,
      dynamicEconomy: dto.dynamicEconomy,
      factionWars: dto.factionWars,
      locationDensity: dto.locationDensity,
      npcDensity: dto.npcDensity,
      resourceAbundance: dto.resourceAbundance,
      dangerLevel: dto.dangerLevel,
      globalRadiation: dto.globalRadiation,
      radiationZones: dto.radiationZones,
      mutationRate: 10 // базовое значение
    }

    return {
      seed: dto.seed,
      worldSize: {
        width: dto.worldWidth,
        height: dto.worldHeight
      },
      settings,
      userId: dto.userId,
      generateWithAI: dto.generateWithAI,
      aiPromptContext: dto.aiPromptContext
    }
  }

  /**
   * Обновляет статус генерации
   */
  private updateGenerationStatus(worldId: string, updates: Partial<WorldGenerationStatusDto>): void {
    const currentStatus = this.generationStatuses.get(worldId)

    if (currentStatus) {
      const updatedStatus = { ...currentStatus, ...updates }
      this.generationStatuses.set(worldId, updatedStatus)
    }
  }

  /**
   * Генерирует уникальный ID мира
   */
  private generateWorldId(seed: string, userId: string): string {
    const timestamp = Date.now()
    const cleanSeed = seed.replace(/[^a-zA-Z0-9]/g, '_')
    return `world_${userId}_${cleanSeed}_${timestamp}`
  }
}
