import { Injectable, Logger, NotFoundException } from '@nestjs/common'
import { CreateWorldDto, WorldGenerationStatusDto, WorldPresetDto } from './dto/world-generation.dto'

/**
 * Упрощенный сервис для генерации игровых миров
 */
@Injectable()
export class WorldGenerationService {
  private readonly logger = new Logger(WorldGenerationService.name)
  
  // Хранилище статусов генерации
  private generationStatuses = new Map<string, WorldGenerationStatusDto>()
  
  // Хранилище сгенерированных миров
  private generatedWorlds = new Map<string, any>()

  constructor() {}

  /**
   * Создает новый мир
   */
  async createWorld(createWorldDto: CreateWorldDto): Promise<{ worldId: string; status: string }> {
    const worldId = this.generateWorldId(createWorldDto.seed, createWorldDto.userId)
    
    this.logger.log(`Starting world generation for ${worldId}`)
    
    // Инициализируем статус
    this.updateGenerationStatus(worldId, {
      worldId,
      status: 'in_progress',
      progress: 0,
      currentStage: 'initialization',
      currentTask: 'Инициализация генерации мира...',
      startedAt: new Date()
    })

    // Запускаем генерацию асинхронно
    this.startWorldGeneration(worldId, createWorldDto).catch(error => {
      this.logger.error(`World generation failed for ${worldId}:`, error)
      this.updateGenerationStatus(worldId, {
        status: 'failed',
        progress: 0,
        currentStage: 'failed',
        currentTask: 'Ошибка генерации мира',
        error: error.message
      })
    })

    return {
      worldId,
      status: 'started'
    }
  }

  /**
   * Получает статус генерации мира
   */
  async getGenerationStatus(worldId: string): Promise<WorldGenerationStatusDto> {
    const status = this.generationStatuses.get(worldId)
    
    if (!status) {
      throw new NotFoundException(`Generation status not found for world ID: ${worldId}`)
    }
    
    return status
  }

  /**
   * Получает сгенерированный мир
   */
  async getWorld(worldId: string, userId?: string): Promise<any> {
    const world = this.generatedWorlds.get(worldId)
    
    if (!world) {
      throw new NotFoundException(`World not found for ID: ${worldId}`)
    }
    
    return world
  }

  /**
   * Получает список предустановок мира
   */
  async getPresets(): Promise<WorldPresetDto[]> {
    return [
      {
        id: 'wasteland',
        name: 'Пустошь',
        description: 'Классический постапокалиптический мир',
        settings: {
          difficulty: 'normal',
          algorithm: 'hybrid',
          locationDensity: 50,
          npcDensity: 40,
          dangerLevel: 45
        }
      },
      {
        id: 'hardcore',
        name: 'Хардкор',
        description: 'Максимальная сложность выживания',
        settings: {
          difficulty: 'nightmare',
          algorithm: 'cellular',
          locationDensity: 30,
          npcDensity: 60,
          dangerLevel: 80
        }
      }
    ]
  }

  /**
   * Получает шаблоны генерации
   */
  async getTemplates(type?: string): Promise<any[]> {
    const templates = {
      world: [
        { id: 'nuclear', name: 'Ядерная пустошь', description: 'Мир после ядерной войны' },
        { id: 'bio', name: 'Биологическая катастрофа', description: 'Мир после биологической катастрофы' }
      ],
      location: [
        { id: 'bunker', name: 'Бункер', description: 'Подземное убежище' },
        { id: 'city', name: 'Руины города', description: 'Разрушенный город' }
      ]
    }

    return type ? templates[type] || [] : Object.values(templates).flat()
  }

  /**
   * Асинхронная генерация мира
   */
  private async startWorldGeneration(worldId: string, createWorldDto: CreateWorldDto): Promise<void> {
    try {
      // Этап 1: Создание базовой структуры
      this.updateGenerationStatus(worldId, {
        progress: 10,
        currentStage: 'world_structure',
        currentTask: 'Создание базовой структуры мира...'
      })
      
      await this.delay(1000)

      // Этап 2: Генерация ландшафта
      this.updateGenerationStatus(worldId, {
        progress: 30,
        currentStage: 'terrain_generation',
        currentTask: 'Генерация ландшафта...'
      })
      
      await this.delay(2000)

      // Этап 3: Создание локаций
      this.updateGenerationStatus(worldId, {
        progress: 50,
        currentStage: 'location_generation',
        currentTask: 'Создание локаций...'
      })
      
      await this.delay(1500)

      // Этап 4: Генерация NPC
      this.updateGenerationStatus(worldId, {
        progress: 70,
        currentStage: 'npc_generation',
        currentTask: 'Создание персонажей...'
      })
      
      await this.delay(1000)

      // Этап 5: Создание фракций
      this.updateGenerationStatus(worldId, {
        progress: 85,
        currentStage: 'faction_generation',
        currentTask: 'Создание фракций...'
      })
      
      await this.delay(500)

      // Создаем мок-мир
      const world = this.createMockWorld(worldId, createWorldDto)
      this.generatedWorlds.set(worldId, world)
      
      // Завершение
      this.updateGenerationStatus(worldId, {
        status: 'completed',
        progress: 100,
        currentStage: 'completed',
        currentTask: 'Генерация мира завершена!',
        completedAt: new Date(),
        stats: {
          locationsGenerated: 25,
          npcsGenerated: 50,
          factionsGenerated: 4,
          cellsGenerated: createWorldDto.worldWidth * createWorldDto.worldHeight
        }
      })
      
      this.logger.log(`World generation completed for ${worldId}`)
      
    } catch (error) {
      throw new Error(`World generation failed: ${error.message}`)
    }
  }

  /**
   * Создает мок-мир для тестирования
   */
  private createMockWorld(worldId: string, createWorldDto: CreateWorldDto): any {
    return {
      id: worldId,
      metadata: {
        name: `Мир ${createWorldDto.seed}`,
        description: 'Сгенерированный тестовый мир',
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        createdBy: createWorldDto.userId,
        tags: ['generated', 'test'],
        version: '1.0.0',
        isPublic: false
      },
      settings: {
        seed: createWorldDto.seed,
        worldSize: {
          width: createWorldDto.worldWidth,
          height: createWorldDto.worldHeight
        },
        difficulty: createWorldDto.difficulty,
        generationAlgorithm: createWorldDto.algorithm,
        locationDensity: createWorldDto.locationDensity,
        npcDensity: createWorldDto.npcDensity,
        resourceAbundance: createWorldDto.resourceAbundance,
        dangerLevel: createWorldDto.dangerLevel,
        globalRadiation: createWorldDto.globalRadiation,
        radiationZones: createWorldDto.radiationZones,
        weatherSystem: createWorldDto.weatherSystem,
        seasonalChanges: createWorldDto.seasonalChanges,
        dynamicEconomy: createWorldDto.dynamicEconomy,
        factionWars: createWorldDto.factionWars,
        timeScale: createWorldDto.timeScale,
        dayDuration: createWorldDto.dayDuration
      },
      worldGrid: this.generateMockGrid(createWorldDto.worldWidth, createWorldDto.worldHeight),
      locations: this.generateMockLocations(),
      npcs: this.generateMockNPCs(),
      factions: this.generateMockFactions(),
      state: {
        currentTime: 0,
        daysPassed: 0,
        weather: {
          current: 'clear',
          temperature: 20
        }
      }
    }
  }

  /**
   * Генерирует мок-сетку мира
   */
  private generateMockGrid(width: number, height: number): any[][] {
    const grid = []
    for (let y = 0; y < height; y++) {
      const row = []
      for (let x = 0; x < width; x++) {
        row.push({
          x,
          y,
          terrain: 'wasteland',
          elevation: Math.random() * 100,
          radiationLevel: Math.random() * 50,
          resources: {},
          isExplored: false,
          isVisible: false
        })
      }
      grid.push(row)
    }
    return grid
  }

  /**
   * Генерирует мок-локации
   */
  private generateMockLocations(): Record<string, any> {
    return {
      'loc_1': {
        id: 'loc_1',
        name: 'Заброшенный бункер',
        type: 'bunker',
        description: 'Старый военный бункер',
        position: { x: 10, y: 10 },
        size: { width: 5, height: 5 },
        npcs: ['npc_1'],
        resources: { 'scrap': 100 },
        dangerLevel: 30,
        radiationLevel: 10,
        isDiscovered: false,
        isAccessible: true
      }
    }
  }

  /**
   * Генерирует мок-NPC
   */
  private generateMockNPCs(): Record<string, any> {
    return {
      'npc_1': {
        id: 'npc_1',
        name: 'Старый Джо',
        type: 'trader',
        description: 'Торговец металлоломом',
        currentLocationId: 'loc_1',
        level: 5,
        health: 100,
        maxHealth: 100,
        stats: { strength: 10, agility: 8, intelligence: 12, charisma: 15 },
        inventory: { 'caps': 500 },
        isHostile: false,
        isTrader: true
      }
    }
  }

  /**
   * Генерирует мок-фракции
   */
  private generateMockFactions(): Record<string, any> {
    return {
      'faction_1': {
        id: 'faction_1',
        name: 'Торговцы Пустоши',
        description: 'Объединение торговцев',
        alignment: 'neutral',
        reputation: 0,
        territory: ['loc_1'],
        resources: { 'caps': 10000 },
        memberCount: 50,
        isPlayerFaction: false
      }
    }
  }

  /**
   * Обновляет статус генерации
   */
  private updateGenerationStatus(worldId: string, updates: Partial<WorldGenerationStatusDto>): void {
    const currentStatus = this.generationStatuses.get(worldId) || {
      worldId,
      status: 'pending',
      progress: 0,
      currentStage: '',
      currentTask: '',
      startedAt: new Date()
    }

    const updatedStatus = { ...currentStatus, ...updates }
    this.generationStatuses.set(worldId, updatedStatus)
  }

  /**
   * Генерирует уникальный ID мира
   */
  private generateWorldId(seed: string, userId: string): string {
    const timestamp = Date.now()
    const cleanSeed = seed.replace(/[^a-zA-Z0-9]/g, '_')
    return `world_${userId}_${cleanSeed}_${timestamp}`
  }

  /**
   * Задержка для имитации работы
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
