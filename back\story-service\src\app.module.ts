import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { HttpModule } from '@nestjs/axios'
import { StoryModule } from './story/story.module'
import { AiModule } from './ai/ai.module'
import { WorldGenerationModule } from './world-generation/world-generation.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    HttpModule,
    StoryModule,
    AiModule,
    WorldGenerationModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
