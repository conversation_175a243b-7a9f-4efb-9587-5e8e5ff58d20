import { World } from '@shared/types/World'

const API_BASE = '/api'

// Типы для API
export interface CreateWorldRequest {
  seed: string
  userId: string
  worldWidth: number
  worldHeight: number
  algorithm: 'perlin' | 'cellular' | 'voronoi' | 'hybrid'
  difficulty: 'easy' | 'normal' | 'hard' | 'nightmare'
  locationDensity: number
  npcDensity: number
  resourceAbundance: number
  dangerLevel: number
  globalRadiation: number
  radiationZones: boolean
  weatherSystem: boolean
  seasonalChanges: boolean
  dynamicEconomy: boolean
  factionWars: boolean
  generateWithAI: boolean
  aiPromptContext?: string
  timeScale: number
  dayDuration: number
}

export interface WorldGenerationStatus {
  worldId: string
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  progress: number
  currentStage: string
  currentTask: string
  estimatedTimeRemaining?: number
  error?: string
  stats?: {
    locationsGenerated: number
    npcsGenerated: number
    factionsGenerated: number
    cellsGenerated: number
  }
  startedAt: string
  completedAt?: string
}

export interface WorldSummary {
  id: string
  userId: string
  seed: string
  name: string
  description: string
  metadata: {
    worldSize: { width: number; height: number }
    difficulty: string
    algorithm: string
    stats: {
      locationsCount: number
      npcsCount: number
      factionsCount: number
      cellsCount: number
    }
    tags: string[]
    generatorVersion: string
    generationTime: number
    generatedWithAI: boolean
  }
  status: 'active' | 'archived' | 'deleted'
  isPublic: boolean
  loadCount: number
  lastLoadedAt: string
  sizeBytes: number
  version: string
  createdAt: string
  updatedAt: string
}

export interface WorldsListResponse {
  success: boolean
  worlds: WorldSummary[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

export interface UserWorldsStats {
  totalWorlds: number
  activeWorlds: number
  archivedWorlds: number
  totalLoadCount: number
  averageWorldSize: number
}

/**
 * API для работы с мирами
 */
export class WorldsApi {
  /**
   * Создает новый мир
   */
  static async createWorld(params: CreateWorldRequest): Promise<{ worldId: string; status: string }> {
    const response = await fetch(`${API_BASE}/world-generation/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    })

    if (!response.ok) {
      throw new Error(`Failed to create world: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Получает статус генерации мира
   */
  static async getGenerationStatus(worldId: string): Promise<WorldGenerationStatus> {
    const response = await fetch(`${API_BASE}/world-generation/status/${worldId}`)

    if (!response.ok) {
      throw new Error(`Failed to get generation status: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Получает сгенерированный мир (полные данные)
   */
  static async getWorld(worldId: string, userId?: string): Promise<World> {
    const url = new URL(`${API_BASE}/world-generation/world/${worldId}`, window.location.origin)
    if (userId) {
      url.searchParams.set('userId', userId)
    }

    const response = await fetch(url.toString())

    if (!response.ok) {
      throw new Error(`Failed to get world: ${response.statusText}`)
    }

    const data = await response.json()
    return data.world
  }

  /**
   * Получает список миров пользователя (краткая информация)
   */
  static async getUserWorlds(
    userId: string,
    page: number = 1,
    limit: number = 10,
    status: 'active' | 'archived' | 'all' = 'active'
  ): Promise<WorldsListResponse> {
    const url = new URL(`${API_BASE}/worlds/user/${userId}`, window.location.origin)
    url.searchParams.set('page', page.toString())
    url.searchParams.set('limit', limit.toString())
    url.searchParams.set('status', status)

    const response = await fetch(url.toString())

    if (!response.ok) {
      throw new Error(`Failed to get user worlds: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Получает публичные миры
   */
  static async getPublicWorlds(
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<WorldsListResponse> {
    const url = new URL(`${API_BASE}/worlds/public/list`, window.location.origin)
    url.searchParams.set('page', page.toString())
    url.searchParams.set('limit', limit.toString())
    if (search) {
      url.searchParams.set('search', search)
    }

    const response = await fetch(url.toString())

    if (!response.ok) {
      throw new Error(`Failed to get public worlds: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Обновляет информацию о мире
   */
  static async updateWorld(
    worldId: string,
    userId: string,
    updates: {
      name?: string
      description?: string
      isPublic?: boolean
      status?: 'active' | 'archived'
    }
  ): Promise<{ success: boolean; world: WorldSummary }> {
    const response = await fetch(`${API_BASE}/worlds/${worldId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, ...updates }),
    })

    if (!response.ok) {
      throw new Error(`Failed to update world: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Архивирует мир
   */
  static async archiveWorld(worldId: string, userId: string): Promise<{ success: boolean }> {
    const response = await fetch(`${API_BASE}/worlds/${worldId}/archive`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    })

    if (!response.ok) {
      throw new Error(`Failed to archive world: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Удаляет мир
   */
  static async deleteWorld(worldId: string, userId: string): Promise<{ success: boolean }> {
    const url = new URL(`${API_BASE}/worlds/${worldId}`, window.location.origin)
    url.searchParams.set('userId', userId)

    const response = await fetch(url.toString(), {
      method: 'DELETE',
    })

    if (!response.ok) {
      throw new Error(`Failed to delete world: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Получает статистику миров пользователя
   */
  static async getUserWorldsStats(userId: string): Promise<UserWorldsStats> {
    const response = await fetch(`${API_BASE}/worlds/user/${userId}/stats`)

    if (!response.ok) {
      throw new Error(`Failed to get user worlds stats: ${response.statusText}`)
    }

    const data = await response.json()
    return data.stats
  }

  /**
   * Поиск миров по seed
   */
  static async findWorldsBySeed(seed: string): Promise<{ worlds: WorldSummary[]; count: number }> {
    const response = await fetch(`${API_BASE}/worlds/search/seed/${encodeURIComponent(seed)}`)

    if (!response.ok) {
      throw new Error(`Failed to search worlds by seed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Получает предустановки мира
   */
  static async getWorldPresets(): Promise<any[]> {
    const response = await fetch(`${API_BASE}/world-generation/presets`)

    if (!response.ok) {
      throw new Error(`Failed to get world presets: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Получает шаблоны генерации
   */
  static async getGenerationTemplates(type?: string): Promise<any[]> {
    const url = new URL(`${API_BASE}/world-generation/templates`, window.location.origin)
    if (type) {
      url.searchParams.set('type', type)
    }

    const response = await fetch(url.toString())

    if (!response.ok) {
      throw new Error(`Failed to get generation templates: ${response.statusText}`)
    }

    return response.json()
  }
}
