import { Injectable, Logger } from '@nestjs/common'
import { Faction, FactionAlignment } from '@shared/types/Faction'
import { FactionGenerationParams } from './types/world-generation.types'
import { AIHelper } from './ai.helper'

/**
 * Генератор фракций
 * Создает фракции для мира с учетом настроек и AI
 */
@Injectable()
export class FactionGenerator {
  private readonly logger = new Logger(FactionGenerator.name)
  
  constructor(private readonly aiHelper: AIHelper) {}

  /**
   * Генерирует фракции для мира
   */
  async generateFactions(params: FactionGenerationParams): Promise<Faction[]> {
    this.logger.log('Starting faction generation...')
    
    const factions: Faction[] = []
    
    // Генерируем основные фракции
    const mainFactions = await this.generateMainFactions(params)
    factions.push(...mainFactions)
    
    // Генерируем дополнительные фракции если нужно
    const additionalCount = params.maxFactions - mainFactions.length
    if (additionalCount > 0) {
      const additionalFactions = await this.generateAdditionalFactions(params, additionalCount)
      factions.push(...additionalFactions)
    }
    
    // Устанавливаем отношения между фракциями
    this.establishFactionRelationships(factions)
    
    this.logger.log(`Generated ${factions.length} factions`)
    return factions
  }

  /**
   * Генерирует основные фракции (всегда присутствуют)
   */
  private async generateMainFactions(params: FactionGenerationParams): Promise<Faction[]> {
    const factions: Faction[] = []
    
    // Фракция выживших (нейтральная)
    factions.push(this.createSurvivorsFaction(params.worldSeed))
    
    // Военная фракция (если высокий уровень опасности)
    if (params.worldSettings.dangerLevel > 50) {
      factions.push(this.createMilitaryFaction(params.worldSeed))
    }
    
    // Торговая фракция (если включена экономика)
    if (params.worldSettings.dynamicEconomy) {
      factions.push(this.createTradersFaction(params.worldSeed))
    }
    
    return factions
  }

  /**
   * Генерирует дополнительные фракции
   */
  private async generateAdditionalFactions(
    params: FactionGenerationParams, 
    count: number
  ): Promise<Faction[]> {
    const factions: Faction[] = []
    
    for (let i = 0; i < count; i++) {
      const faction = await this.generateRandomFaction(params, i)
      factions.push(faction)
    }
    
    return factions
  }

  /**
   * Создает фракцию выживших
   */
  private createSurvivorsFaction(worldSeed: string): Faction {
    return {
      id: `faction_survivors_${worldSeed}`,
      name: 'Выжившие',
      description: 'Группа обычных людей, пытающихся выжить в постапокалиптическом мире.',
      shortName: 'Выжившие',
      
      alignment: FactionAlignment.NEUTRAL,
      ideology: 'Выживание любой ценой',
      goals: ['Найти безопасное место', 'Обеспечить ресурсами', 'Защитить слабых'],
      
      leaderId: undefined,
      leaderTitle: 'Старейшина',
      governmentType: 'democracy',
      
      ranks: [
        {
          id: 'newcomer',
          name: 'Новичок',
          level: 1,
          permissions: ['basic_trade'],
          requirements: { reputation: 0 }
        },
        {
          id: 'member',
          name: 'Член общины',
          level: 2,
          permissions: ['basic_trade', 'shelter_access'],
          requirements: { reputation: 25 }
        },
        {
          id: 'elder',
          name: 'Старейшина',
          level: 3,
          permissions: ['basic_trade', 'shelter_access', 'leadership'],
          requirements: { reputation: 75 }
        }
      ],
      
      memberCount: Math.floor(Math.random() * 50) + 20,
      recruitmentOpen: true,
      
      territory: {
        controlledLocations: [],
        influenceZones: [],
        totalArea: 0,
        borders: []
      },
      totalInfluence: 30,
      isExpanding: false,
      
      relationships: [],
      
      playerReputation: 0,
      
      resources: [
        {
          type: 'food',
          amount: 100,
          maxCapacity: 500,
          productionRate: 5,
          consumptionRate: 8
        },
        {
          type: 'water',
          amount: 80,
          maxCapacity: 200,
          productionRate: 3,
          consumptionRate: 6
        }
      ],
      currency: 500,
      tradeRoutes: [],
      
      militaryStrength: 20,
      defenseRating: 30,
      aggressionLevel: 10,
      
      availableQuests: [],
      
      technologyLevel: 25,
      specialAbilities: ['scavenging', 'basic_crafting'],
      uniqueItems: [],
      
      colors: {
        primary: '#8B4513',
        secondary: '#D2691E'
      },
      
      foundedYear: 2077,
      history: 'Образовалась из группы выживших после Великой войны.',
      motto: 'Вместе мы сильнее',
      
      isActive: true,
      isPlayable: true,
      isHostileToPlayer: false,
      isEssential: true,
      
      currentEvents: [],
      scheduledEvents: [],
      
      createdAt: new Date(),
      lastActivityAt: new Date()
    }
  }

  /**
   * Создает военную фракцию
   */
  private createMilitaryFaction(worldSeed: string): Faction {
    return {
      id: `faction_military_${worldSeed}`,
      name: 'Остатки армии',
      description: 'Выжившие военные, пытающиеся восстановить порядок.',
      shortName: 'Армия',
      
      alignment: FactionAlignment.LAWFUL,
      ideology: 'Порядок и дисциплина',
      goals: ['Восстановить порядок', 'Защитить мирных жителей', 'Найти правительство'],
      
      leaderId: undefined,
      leaderTitle: 'Командир',
      governmentType: 'autocracy',
      
      ranks: [
        {
          id: 'recruit',
          name: 'Рекрут',
          level: 1,
          permissions: ['basic_equipment'],
          requirements: { reputation: 0 }
        },
        {
          id: 'soldier',
          name: 'Солдат',
          level: 2,
          permissions: ['basic_equipment', 'weapon_access'],
          requirements: { reputation: 40 }
        },
        {
          id: 'officer',
          name: 'Офицер',
          level: 3,
          permissions: ['basic_equipment', 'weapon_access', 'command'],
          requirements: { reputation: 80 }
        }
      ],
      
      memberCount: Math.floor(Math.random() * 30) + 15,
      recruitmentOpen: true,
      recruitmentRequirements: {
        minimumLevel: 3,
        skills: { 'combat': 25 }
      },
      
      territory: {
        controlledLocations: [],
        influenceZones: [],
        totalArea: 0,
        borders: []
      },
      totalInfluence: 50,
      isExpanding: true,
      
      relationships: [],
      
      playerReputation: 0,
      
      resources: [
        {
          type: 'weapons',
          amount: 50,
          maxCapacity: 200,
          productionRate: 2,
          consumptionRate: 1
        },
        {
          type: 'ammunition',
          amount: 200,
          maxCapacity: 1000,
          productionRate: 10,
          consumptionRate: 15
        }
      ],
      currency: 1000,
      tradeRoutes: [],
      
      militaryStrength: 80,
      defenseRating: 90,
      aggressionLevel: 40,
      
      availableQuests: [],
      
      technologyLevel: 60,
      specialAbilities: ['military_tactics', 'weapon_maintenance'],
      uniqueItems: ['military_radio', 'combat_armor'],
      
      colors: {
        primary: '#2F4F2F',
        secondary: '#556B2F'
      },
      
      foundedYear: 2077,
      history: 'Остатки довоенной армии, продолжающие выполнять свой долг.',
      motto: 'Честь и долг превыше всего',
      
      isActive: true,
      isPlayable: true,
      isHostileToPlayer: false,
      isEssential: false,
      
      currentEvents: [],
      scheduledEvents: [],
      
      createdAt: new Date(),
      lastActivityAt: new Date()
    }
  }

  /**
   * Создает торговую фракцию
   */
  private createTradersFaction(worldSeed: string): Faction {
    return {
      id: `faction_traders_${worldSeed}`,
      name: 'Караванщики',
      description: 'Торговцы, путешествующие между поселениями.',
      shortName: 'Торговцы',
      
      alignment: FactionAlignment.NEUTRAL,
      ideology: 'Прибыль превыше всего',
      goals: ['Развивать торговлю', 'Находить новые рынки', 'Защищать караваны'],
      
      leaderId: undefined,
      leaderTitle: 'Торговый магнат',
      governmentType: 'oligarchy',
      
      ranks: [
        {
          id: 'customer',
          name: 'Покупатель',
          level: 1,
          permissions: ['basic_trade'],
          requirements: { reputation: 0 }
        },
        {
          id: 'partner',
          name: 'Партнер',
          level: 2,
          permissions: ['basic_trade', 'bulk_discount'],
          requirements: { reputation: 50 }
        },
        {
          id: 'merchant',
          name: 'Торговец',
          level: 3,
          permissions: ['basic_trade', 'bulk_discount', 'exclusive_goods'],
          requirements: { reputation: 90 }
        }
      ],
      
      memberCount: Math.floor(Math.random() * 40) + 10,
      recruitmentOpen: true,
      
      territory: {
        controlledLocations: [],
        influenceZones: [],
        totalArea: 0,
        borders: []
      },
      totalInfluence: 40,
      isExpanding: true,
      
      relationships: [],
      
      playerReputation: 0,
      
      resources: [
        {
          type: 'goods',
          amount: 300,
          maxCapacity: 1000,
          productionRate: 20,
          consumptionRate: 25
        }
      ],
      currency: 2000,
      tradeRoutes: [],
      
      militaryStrength: 30,
      defenseRating: 40,
      aggressionLevel: 5,
      
      availableQuests: [],
      
      technologyLevel: 45,
      specialAbilities: ['trade_expertise', 'caravan_management'],
      uniqueItems: ['trade_goods', 'caravan_supplies'],
      
      colors: {
        primary: '#DAA520',
        secondary: '#B8860B'
      },
      
      foundedYear: 2078,
      history: 'Образовалась из группы предприимчивых торговцев.',
      motto: 'Торговля объединяет мир',
      
      isActive: true,
      isPlayable: true,
      isHostileToPlayer: false,
      isEssential: false,
      
      currentEvents: [],
      scheduledEvents: [],
      
      createdAt: new Date(),
      lastActivityAt: new Date()
    }
  }

  /**
   * Генерирует случайную фракцию
   */
  private async generateRandomFaction(params: FactionGenerationParams, index: number): Promise<Faction> {
    const factionTypes = ['raiders', 'cultists', 'scientists', 'mutants']
    const type = factionTypes[Math.floor(Math.random() * factionTypes.length)]
    
    return this.createFactionByType(type, params.worldSeed, index)
  }

  /**
   * Создает фракцию по типу
   */
  private createFactionByType(type: string, worldSeed: string, index: number): Faction {
    const baseId = `faction_${type}_${index}_${worldSeed}`
    
    // Базовая структура фракции
    const baseFaction: Partial<Faction> = {
      id: baseId,
      alignment: FactionAlignment.NEUTRAL,
      governmentType: 'autocracy',
      memberCount: Math.floor(Math.random() * 30) + 10,
      recruitmentOpen: Math.random() > 0.5,
      territory: {
        controlledLocations: [],
        influenceZones: [],
        totalArea: 0,
        borders: []
      },
      totalInfluence: Math.floor(Math.random() * 50) + 10,
      isExpanding: Math.random() > 0.6,
      relationships: [],
      playerReputation: 0,
      resources: [],
      currency: Math.floor(Math.random() * 1000) + 200,
      tradeRoutes: [],
      availableQuests: [],
      currentEvents: [],
      scheduledEvents: [],
      isActive: true,
      isPlayable: true,
      isHostileToPlayer: Math.random() > 0.7,
      isEssential: false,
      createdAt: new Date(),
      lastActivityAt: new Date()
    }

    // Специфичные настройки по типу
    switch (type) {
      case 'raiders':
        return {
          ...baseFaction,
          name: `Рейдеры ${index + 1}`,
          description: 'Агрессивная группа мародеров.',
          shortName: 'Рейдеры',
          alignment: FactionAlignment.CHAOTIC,
          ideology: 'Сила решает все',
          goals: ['Грабить караваны', 'Захватывать территории'],
          leaderTitle: 'Главарь',
          militaryStrength: 70,
          aggressionLevel: 80,
          colors: { primary: '#8B0000', secondary: '#A0522D' }
        } as Faction
        
      case 'cultists':
        return {
          ...baseFaction,
          name: `Культ ${index + 1}`,
          description: 'Религиозная секта с странными верованиями.',
          shortName: 'Культисты',
          alignment: FactionAlignment.CHAOTIC,
          ideology: 'Вера в новый мир',
          goals: ['Распространять учение', 'Найти артефакты'],
          leaderTitle: 'Пророк',
          militaryStrength: 40,
          aggressionLevel: 30,
          colors: { primary: '#4B0082', secondary: '#8A2BE2' }
        } as Faction
        
      default:
        return baseFaction as Faction
    }
  }

  /**
   * Устанавливает отношения между фракциями
   */
  private establishFactionRelationships(factions: Faction[]): void {
    for (let i = 0; i < factions.length; i++) {
      for (let j = i + 1; j < factions.length; j++) {
        const faction1 = factions[i]
        const faction2 = factions[j]
        
        const relationship = this.calculateRelationship(faction1, faction2)
        
        // Добавляем отношения в обе фракции
        faction1.relationships.push({
          factionId: faction2.id,
          status: relationship,
          reputation: this.getReputationByStatus(relationship),
          lastInteraction: new Date(),
          history: []
        })
        
        faction2.relationships.push({
          factionId: faction1.id,
          status: relationship,
          reputation: this.getReputationByStatus(relationship),
          lastInteraction: new Date(),
          history: []
        })
      }
    }
  }

  /**
   * Вычисляет отношения между фракциями
   */
  private calculateRelationship(faction1: Faction, faction2: Faction): 'allied' | 'friendly' | 'neutral' | 'hostile' | 'enemy' {
    // Простая логика на основе идеологий и целей
    if (faction1.alignment === faction2.alignment) {
      return Math.random() > 0.5 ? 'friendly' : 'neutral'
    }
    
    if (faction1.aggressionLevel > 60 || faction2.aggressionLevel > 60) {
      return Math.random() > 0.7 ? 'hostile' : 'neutral'
    }
    
    return 'neutral'
  }

  /**
   * Получает репутацию по статусу отношений
   */
  private getReputationByStatus(status: string): number {
    const reputationMap = {
      'allied': 80,
      'friendly': 50,
      'neutral': 0,
      'hostile': -50,
      'enemy': -80
    }
    
    return reputationMap[status] || 0
  }
}
