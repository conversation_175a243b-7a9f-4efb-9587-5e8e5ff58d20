import React, { useState } from 'react'
import { Plus, Trash2, Play, Calendar, Clock, BarChart3 } from 'lucide-react'
import { useGameStore, World } from '../store/gameStore'
import { useAuthStore } from '../store/authStore'
import styles from './WorldList.module.css'

interface WorldListProps {
  onWorldSelect: (worldId: string) => void
}

const WorldList: React.FC<WorldListProps> = ({ onWorldSelect }) => {
  const {
    worlds,
    addWorld,
    deleteWorld,
    loadWorld,
    createWorldOnServer,
    isCreatingWorld,
    creationProgress,
    creationStatus,
    creationError,
    resetCreationState
  } = useGameStore()
  const { user } = useAuthStore()
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newWorldName, setNewWorldName] = useState('')
  const [newWorldDescription, setNewWorldDescription] = useState('')
  const [newWorldSeed, setNewWorldSeed] = useState('')

  const handleCreateWorld = async () => {
    if (newWorldName.trim() && user) {
      try {
        // Генерируем seed если не указан
        const seed = newWorldSeed.trim() || `${newWorldName.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}`

        await createWorldOnServer({
          name: newWorldName.trim(),
          description: newWorldDescription.trim() || undefined,
          seed: seed,
          userId: user.id,
          worldWidth: 100,
          worldHeight: 100,
          algorithm: 'hybrid',
          difficulty: 'normal',
          generateWithAI: true
        })

        // Очищаем форму только после успешного создания
        setNewWorldName('')
        setNewWorldDescription('')
        setNewWorldSeed('')
        setShowCreateForm(false)

      } catch (error) {
        console.error('Failed to create world:', error)
        // Ошибка будет отображена через creationError из store
      }
    }
  }

  const handleDeleteWorld = (worldId: string, worldName: string) => {
    if (confirm(`Вы уверены, что хотите удалить мир "${worldName}"? Это действие нельзя отменить.`)) {
      deleteWorld(worldId)
    }
  }

  const handleLoadWorld = (worldId: string) => {
    loadWorld(worldId)
    onWorldSelect(worldId)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className={styles.worldList}>
      {/* Create New World Button */}
      <div className={styles.createButton}>
        <button
          onClick={() => setShowCreateForm(true)}
          className={styles.createWorldButton}
        >
          <Plus />
          Создать новый мир
        </button>
      </div>

      {/* Create World Form */}
      {showCreateForm && (
        <div className={styles.createForm}>
          <h3 className={styles.formTitle}>Создание нового мира</h3>

          {/* Показываем прогресс создания */}
          {isCreatingWorld && (
            <div className={styles.creationProgress}>
              <div className={styles.progressBar}>
                <div
                  className={styles.progressFill}
                  style={{ width: `${creationProgress}%` }}
                />
              </div>
              <p className={styles.progressText}>{creationProgress}%</p>
              <p className={styles.statusText}>{creationStatus}</p>
            </div>
          )}

          {/* Показываем ошибку если есть */}
          {creationError && (
            <div className={styles.errorMessage}>
              <p>❌ Ошибка: {creationError}</p>
              <button
                onClick={resetCreationState}
                className={styles.retryButton}
              >
                Попробовать снова
              </button>
            </div>
          )}

          {!isCreatingWorld && (
            <div className={styles.formFields}>
              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Название мира *
                </label>
                <input
                  type="text"
                  value={newWorldName}
                  onChange={(e) => setNewWorldName(e.target.value)}
                  placeholder="Введите название мира..."
                  className={styles.formInput}
                  maxLength={50}
                />
              </div>

              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Seed мира (необязательно)
                </label>
                <input
                  type="text"
                  value={newWorldSeed}
                  onChange={(e) => setNewWorldSeed(e.target.value)}
                  placeholder="nuclear_wasteland_2077 (автогенерация если пусто)"
                  className={styles.formInput}
                  maxLength={100}
                />
              </div>

              <div className={styles.formField}>
                <label className={styles.formLabel}>
                  Описание (необязательно)
                </label>
                <textarea
                  value={newWorldDescription}
                  onChange={(e) => setNewWorldDescription(e.target.value)}
                  placeholder="Краткое описание мира..."
                  className={`${styles.formInput} ${styles.formTextarea}`}
                  rows={3}
                  maxLength={200}
                />
              </div>

              <div className={styles.formButtons}>
                <button
                  onClick={handleCreateWorld}
                  disabled={!newWorldName.trim() || !user}
                  className={`${styles.formButton} ${styles.createButton}`}
                >
                  🌍 Создать мир на сервере
                </button>
                <button
                  onClick={() => {
                    setShowCreateForm(false)
                    setNewWorldName('')
                    setNewWorldDescription('')
                    setNewWorldSeed('')
                    resetCreationState()
                  }}
                  className={`${styles.formButton} ${styles.cancelButton}`}
                >
                  Отмена
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Worlds List */}
      {worlds.length === 0 ? (
        <div className={styles.emptyState}>
          {/* <div className={styles.emptyIcon}>🌍</div> */}
          <h3 className={styles.emptyTitle}>Нет созданных миров</h3>
          <p className={styles.emptyDescription}>
            Создайте свой первый мир, чтобы начать приключение в постапокалиптической пустоши
          </p>
        </div>
      ) : (
        <div className={styles.worldsGrid}>
          {worlds
            .sort((a, b) => new Date(b.lastPlayed).getTime() - new Date(a.lastPlayed).getTime())
            .map((world) => (
              <div
                key={world.id}
                className={styles.worldCard}
              >
                <div className={styles.worldCardContent}>
                  <div className={styles.worldInfo}>
                    <h3 className={styles.worldName}>{world.name}</h3>
                    {world.description && (
                      <p className={styles.worldDescription}>{world.description}</p>
                    )}

                    <div className={styles.worldMeta}>
                      <div className={styles.metaItem}>
                        <Calendar />
                        <span>Создан: {formatDate(world.createdAt)}</span>
                      </div>
                      <div className={styles.metaItem}>
                        <Clock />
                        <span>Последняя игра: {formatDate(world.lastPlayed)}</span>
                      </div>
                      <div className={styles.metaItem}>
                        <BarChart3 />
                        <span>Прогресс: {world.progress}%</span>
                      </div>
                    </div>
                  </div>

                  <div className={styles.worldActions}>
                    <button
                      onClick={() => handleLoadWorld(world.id)}
                      className={`${styles.actionButton} ${styles.playButton}`}
                    >
                      <Play />
                      Играть
                    </button>
                    <button
                      onClick={() => handleDeleteWorld(world.id, world.name)}
                      className={`${styles.actionButton} ${styles.deleteButton}`}
                      title="Удалить мир"
                    >
                      <Trash2 />
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  )
}

export default WorldList
