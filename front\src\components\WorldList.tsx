import React, { useState } from 'react'
import { Plus, Trash2, Play, Calendar, Clock, BarChart3 } from 'lucide-react'
import { useGameStore, World } from '../store/gameStore'
import styles from './WorldList.module.css'

interface WorldListProps {
  onWorldSelect: (worldId: string) => void
}

const WorldList: React.FC<WorldListProps> = ({ onWorldSelect }) => {
  const { worlds, addWorld, deleteWorld, loadWorld } = useGameStore()
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newWorldName, setNewWorldName] = useState('')
  const [newWorldDescription, setNewWorldDescription] = useState('')

  const handleCreateWorld = () => {
    if (newWorldName.trim()) {
      addWorld({
        name: newWorldName.trim(),
        description: newWorldDescription.trim() || undefined,
        progress: 0
      })
      setNewWorldName('')
      setNewWorldDescription('')
      setShowCreateForm(false)
    }
  }

  const handleDeleteWorld = (worldId: string, worldName: string) => {
    if (confirm(`Вы уверены, что хотите удалить мир "${worldName}"? Это действие нельзя отменить.`)) {
      deleteWorld(worldId)
    }
  }

  const handleLoadWorld = (worldId: string) => {
    loadWorld(worldId)
    onWorldSelect(worldId)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className={styles.worldList}>
      {/* Create New World Button */}
      <div className={styles.createButton}>
        <button
          onClick={() => setShowCreateForm(true)}
          className={styles.createWorldButton}
        >
          <Plus />
          Создать новый мир
        </button>
      </div>

      {/* Create World Form */}
      {showCreateForm && (
        <div className={styles.createForm}>
          <h3 className={styles.formTitle}>Создание нового мира</h3>
          <div className={styles.formFields}>
            <div className={styles.formField}>
              <label className={styles.formLabel}>
                Название мира *
              </label>
              <input
                type="text"
                value={newWorldName}
                onChange={(e) => setNewWorldName(e.target.value)}
                placeholder="Введите название мира..."
                className={styles.formInput}
                maxLength={50}
              />
            </div>
            <div className={styles.formField}>
              <label className={styles.formLabel}>
                Описание (необязательно)
              </label>
              <textarea
                value={newWorldDescription}
                onChange={(e) => setNewWorldDescription(e.target.value)}
                placeholder="Краткое описание мира..."
                className={`${styles.formInput} ${styles.formTextarea}`}
                rows={3}
                maxLength={200}
              />
            </div>
            <div className={styles.formButtons}>
              <button
                onClick={handleCreateWorld}
                disabled={!newWorldName.trim()}
                className={`${styles.formButton} ${styles.createButton}`}
              >
                Создать
              </button>
              <button
                onClick={() => {
                  setShowCreateForm(false)
                  setNewWorldName('')
                  setNewWorldDescription('')
                }}
                className={`${styles.formButton} ${styles.cancelButton}`}
              >
                Отмена
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Worlds List */}
      {worlds.length === 0 ? (
        <div className={styles.emptyState}>
          {/* <div className={styles.emptyIcon}>🌍</div> */}
          <h3 className={styles.emptyTitle}>Нет созданных миров</h3>
          <p className={styles.emptyDescription}>
            Создайте свой первый мир, чтобы начать приключение в постапокалиптической пустоши
          </p>
        </div>
      ) : (
        <div className={styles.worldsGrid}>
          {worlds
            .sort((a, b) => new Date(b.lastPlayed).getTime() - new Date(a.lastPlayed).getTime())
            .map((world) => (
              <div
                key={world.id}
                className={styles.worldCard}
              >
                <div className={styles.worldCardContent}>
                  <div className={styles.worldInfo}>
                    <h3 className={styles.worldName}>{world.name}</h3>
                    {world.description && (
                      <p className={styles.worldDescription}>{world.description}</p>
                    )}

                    <div className={styles.worldMeta}>
                      <div className={styles.metaItem}>
                        <Calendar />
                        <span>Создан: {formatDate(world.createdAt)}</span>
                      </div>
                      <div className={styles.metaItem}>
                        <Clock />
                        <span>Последняя игра: {formatDate(world.lastPlayed)}</span>
                      </div>
                      <div className={styles.metaItem}>
                        <BarChart3 />
                        <span>Прогресс: {world.progress}%</span>
                      </div>
                    </div>
                  </div>

                  <div className={styles.worldActions}>
                    <button
                      onClick={() => handleLoadWorld(world.id)}
                      className={`${styles.actionButton} ${styles.playButton}`}
                    >
                      <Play />
                      Играть
                    </button>
                    <button
                      onClick={() => handleDeleteWorld(world.id, world.name)}
                      className={`${styles.actionButton} ${styles.deleteButton}`}
                      title="Удалить мир"
                    >
                      <Trash2 />
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  )
}

export default WorldList
