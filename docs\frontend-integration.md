# Интеграция фронтенда с системой миров - NuclearStory

## 🎯 Архитектура фронтенда

```
React Components → Custom Hooks → API Layer → Backend Services
```

## 📁 Структура файлов

```
front/src/
├── api/
│   └── worldsApi.ts              # API клиент для работы с мирами
├── hooks/
│   └── useWorlds.ts              # React хуки для управления состоянием
├── components/
│   ├── WorldManager.tsx          # Главный компонент управления мирами
│   ├── WorldCreator.tsx          # Форма создания мира
│   └── WorldsList.tsx            # Список миров пользователя
└── styles/
    └── worlds.css                # Стили для компонентов миров
```

## 🔗 API интеграция

### Маршрутизация запросов

**POST запросы** → `story-service` (генерация мира)
**GET/DELETE запросы** → `save-service` (работа с сохранениями)

```typescript
// Создание мира
POST /api/world-generation/create → story-service:3003

// Получение мира
GET /api/worlds/{id} → save-service:3004

// Удаление мира  
DELETE /api/worlds/{id} → save-service:3004
```

## 🎣 React Hooks

### useWorldCreation
Управляет процессом создания мира с отслеживанием прогресса:

```typescript
const { 
  createWorld,      // Функция создания
  isCreating,       // Флаг процесса
  worldId,          // ID созданного мира
  status,           // Статус генерации
  error,            // Ошибки
  isCompleted       // Завершено ли
} = useWorldCreation()

// Создание мира
await createWorld({
  seed: 'nuclear_2077',
  userId: 'user_123',
  worldWidth: 100,
  worldHeight: 100,
  algorithm: 'hybrid',
  difficulty: 'normal',
  // ... остальные параметры
})
```

### useUserWorlds
Загружает список миров пользователя с пагинацией:

```typescript
const {
  worlds,           // Массив миров
  loading,          // Загрузка
  error,            // Ошибки
  pagination,       // Информация о пагинации
  refresh,          // Обновить список
  loadPage          // Загрузить страницу
} = useUserWorlds(userId, 'active')
```

### useWorld
Загружает полные данные конкретного мира:

```typescript
const {
  world,            // Объект World
  loading,          // Загрузка
  error,            // Ошибки
  refresh           // Обновить
} = useWorld(worldId, userId)
```

### useWorldManagement
Управление мирами (обновление, удаление, архивирование):

```typescript
const {
  updateWorld,      // Обновить мир
  archiveWorld,     // Архивировать
  deleteWorld,      // Удалить
  loading,          // Загрузка операции
  error             // Ошибки
} = useWorldManagement()

// Обновление мира
await updateWorld(worldId, userId, {
  name: 'New Name',
  isPublic: true
})

// Удаление мира
await deleteWorld(worldId, userId)
```

## 🧩 React Components

### WorldManager
Главный компонент с табами:

```tsx
<WorldManager 
  userId="user_123"
  onWorldLoaded={(world) => {
    // Загрузить мир в игровой store
    dispatch(setCurrentWorld(world))
  }}
/>
```

**Функции:**
- Переключение между табами (Список, Создание, Статистика)
- Боковая панель с деталями выбранного мира
- Интеграция всех дочерних компонентов

### WorldCreator
Форма создания мира с прогресс-баром:

```tsx
<WorldCreator
  userId="user_123"
  onWorldCreated={(worldId) => {
    // Переключиться на список миров
    setActiveTab('list')
  }}
/>
```

**Функции:**
- Форма с валидацией параметров
- Отслеживание прогресса генерации
- Отображение статистики созданного мира

### WorldsList
Список миров с управлением:

```tsx
<WorldsList
  userId="user_123"
  onWorldSelect={(worldId) => {
    // Показать детали в боковой панели
    setSelectedWorldId(worldId)
  }}
  onWorldLoad={(worldId) => {
    // Загрузить мир в игру
    loadWorldIntoGame(worldId)
  }}
/>
```

**Функции:**
- Карточки миров с метаданными
- Фильтрация по статусу (активные/архивированные)
- Действия: загрузить, архивировать, удалить, сделать публичным
- Пагинация

## 🎨 UI/UX особенности

### Прогресс генерации
```tsx
// Отображение прогресса
{status && (
  <div className="progress-bar">
    <div 
      className="progress-fill" 
      style={{ width: `${status.progress}%` }}
    />
  </div>
)}
```

### Карточки миров
```tsx
// Карточка мира с метаданными
<div className="world-card">
  <h3>{world.name}</h3>
  <p>Seed: <code>{world.seed}</code></p>
  <div className="world-stats">
    <span>{world.metadata.stats.locationsCount} локаций</span>
    <span>{world.metadata.stats.npcsCount} NPC</span>
  </div>
  <div className="world-actions">
    <button onClick={() => loadWorld(world.id)}>
      🎮 Загрузить
    </button>
  </div>
</div>
```

### Статистика пользователя
```tsx
// Статистика в виде карточек
<div className="stats-grid">
  <div className="stat-card">
    <div className="stat-value">{stats.totalWorlds}</div>
    <div className="stat-label">Всего миров</div>
  </div>
</div>
```

## 🔄 Workflow пользователя

### 1. Создание мира
```
Пользователь заполняет форму
    ↓
POST /api/world-generation/create
    ↓
Отслеживание прогресса через WebSocket/polling
    ↓
Мир сохраняется в PostgreSQL
    ↓
Уведомление о завершении
```

### 2. Загрузка мира
```
Пользователь выбирает мир из списка
    ↓
GET /api/worlds/{id} (краткая информация)
    ↓
Клик "Загрузить"
    ↓
GET /api/world-generation/world/{id} (полные данные)
    ↓
Мир загружается в игровой store
```

### 3. Управление мирами
```
Список миров с действиями:
- 🎮 Загрузить в игру
- 🔒/🔓 Сделать публичным/приватным  
- 📦 Архивировать
- 🗑️ Удалить
```

## 🛠️ Интеграция с игровым store

### Redux/Zustand интеграция
```typescript
// Загрузка мира в игровой store
const loadWorldIntoGame = async (worldId: string) => {
  try {
    // Получаем полные данные мира
    const world = await WorldsApi.getWorld(worldId, userId)
    
    // Сохраняем в игровой store
    dispatch(setCurrentWorld(world))
    
    // Инициализируем игровые системы
    dispatch(initializeGameSystems(world))
    
    // Переходим в игру
    navigate('/game')
    
  } catch (error) {
    console.error('Failed to load world:', error)
  }
}
```

### Store структура
```typescript
interface GameState {
  currentWorld: World | null
  worldLoading: boolean
  worldError: string | null
  
  // Игровые данные из мира
  locations: Record<string, Location>
  npcs: Record<string, NPC>
  factions: Record<string, Faction>
  worldGrid: MapCell[][]
}
```

## 📱 Responsive дизайн

### Мобильная адаптация
```css
@media (max-width: 768px) {
  .worlds-grid {
    grid-template-columns: 1fr;
  }
  
  .world-details-sidebar {
    width: 100%;
    position: relative;
  }
}
```

### Планшетная адаптация
```css
@media (max-width: 1024px) {
  .worlds-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}
```

## 🔧 Конфигурация

### Environment Variables
```env
REACT_APP_API_BASE_URL=http://localhost:80
REACT_APP_STORY_SERVICE_URL=http://localhost:3003
REACT_APP_SAVE_SERVICE_URL=http://localhost:3004
```

### API Base Configuration
```typescript
// api/worldsApi.ts
const API_BASE = process.env.REACT_APP_API_BASE_URL || '/api'
```

## 🚀 Развертывание

### 1. Установка зависимостей
```bash
npm install
```

### 2. Импорт компонентов
```typescript
// В главном App.tsx
import { WorldManager } from './components/WorldManager'
import './styles/worlds.css'

// Использование
<WorldManager 
  userId={currentUser.id}
  onWorldLoaded={handleWorldLoaded}
/>
```

### 3. Настройка роутинга
```typescript
// React Router
<Route path="/worlds" element={<WorldManager userId={user.id} />} />
```

## 🧪 Тестирование

### Unit тесты для хуков
```typescript
// __tests__/useWorlds.test.ts
import { renderHook } from '@testing-library/react'
import { useWorldCreation } from '../hooks/useWorlds'

test('should create world successfully', async () => {
  const { result } = renderHook(() => useWorldCreation())
  
  await act(async () => {
    await result.current.createWorld(mockWorldParams)
  })
  
  expect(result.current.isCompleted).toBe(true)
})
```

### Integration тесты для компонентов
```typescript
// __tests__/WorldManager.test.tsx
import { render, screen } from '@testing-library/react'
import { WorldManager } from '../components/WorldManager'

test('should render world manager tabs', () => {
  render(<WorldManager userId="test-user" />)
  
  expect(screen.getByText('Мои миры')).toBeInTheDocument()
  expect(screen.getByText('Создать мир')).toBeInTheDocument()
})
```

## 📈 Производительность

### Оптимизации
- **Lazy loading** компонентов
- **Мемоизация** дорогих вычислений
- **Виртуализация** длинных списков
- **Debounce** для поиска

### Кэширование
```typescript
// React Query для кэширования
const { data: worlds } = useQuery(
  ['user-worlds', userId],
  () => WorldsApi.getUserWorlds(userId),
  { staleTime: 5 * 60 * 1000 } // 5 минут
)
```

## 🔍 Мониторинг

### Error Boundary
```typescript
<ErrorBoundary fallback={<WorldsErrorFallback />}>
  <WorldManager userId={userId} />
</ErrorBoundary>
```

### Analytics
```typescript
// Отслеживание событий
analytics.track('World Created', {
  worldId,
  algorithm,
  difficulty,
  generationTime
})
```
