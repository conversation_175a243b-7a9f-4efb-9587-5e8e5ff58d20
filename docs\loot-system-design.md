# Система пресетов лута - NuclearStory

## Обзор

Система пресетов лута предназначена для автоматической генерации содержимого контейнеров в зависимости от их уровня, типа локации и сложности игры.

## Уровни контейнеров

### Уровень 0 - Начальный
- **Описание**: Простые контейнеры в безопасных зонах
- **Типы**: Мусорные баки, старые ящики, заброшенные сумки
- **Содержимое**: Базовые ресурсы, простые инструменты
- **Редкость**: 90% Common, 10% Uncommon

### Уровень 5 - Обычный  
- **Описание**: Стандартные контейнеры в жилых зонах
- **Типы**: Шкафы, комоды, кухонные шкафчики
- **Содержимое**: Еда, медикаменты, бытовые предметы
- **Редкость**: 70% Common, 25% Uncommon, 5% Rare

### Уровень 10 - Улучшенный
- **Описание**: Контейнеры в коммерческих/офисных зонах
- **Типы**: Сейфы, картотеки, складские ящики
- **Содержимое**: Ценные ресурсы, инструменты, документы
- **Редкость**: 50% Common, 35% Uncommon, 15% Rare

### Уровень 15 - Продвинутый
- **Описание**: Военные/медицинские контейнеры
- **Типы**: Оружейные шкафы, медицинские сейфы, военные ящики
- **Содержимое**: Оружие, продвинутые медикаменты, спецоборудование
- **Редкость**: 30% Common, 40% Uncommon, 25% Rare, 5% Epic

## Категории редкости

### Common (Обычный)
- **Вероятность**: Высокая
- **Примеры**: Консервы, простые инструменты, базовые материалы
- **Ценность**: 1-50 крышек

### Uncommon (Необычный)
- **Вероятность**: Средняя
- **Примеры**: Стимуляторы, качественные инструменты, редкие материалы
- **Ценность**: 51-200 крышек

### Rare (Редкий)
- **Вероятность**: Низкая
- **Примеры**: Продвинутые медикаменты, специализированное оборудование
- **Ценность**: 201-500 крышек

### Epic (Эпический)
- **Вероятность**: Очень низкая
- **Примеры**: Мощное оружие, уникальные предметы, чертежи
- **Ценность**: 501-1500 крышек

### Legendary (Легендарный)
- **Вероятность**: Крайне редкая
- **Примеры**: Артефакты, уникальное оружие, квестовые предметы
- **Ценность**: 1500+ крышек

## Структура пресета лута

```typescript
interface LootPreset {
  id: string
  name: string
  level: number // 0, 5, 10, 15
  containerTypes: ContainerType[]
  locationTypes: LocationType[]
  
  // Таблицы лута по редкости
  lootTables: {
    common: LootEntry[]
    uncommon: LootEntry[]
    rare: LootEntry[]
    epic: LootEntry[]
    legendary: LootEntry[]
  }
  
  // Вероятности выпадения по редкости
  rarityWeights: {
    common: number
    uncommon: number
    rare: number
    epic: number
    legendary: number
  }
  
  // Количество предметов
  itemCount: {
    min: number
    max: number
  }
  
  // Модификаторы
  modifiers: {
    difficultyMultiplier: number // зависит от сложности мира
    locationDangerBonus: number // бонус за опасные локации
    factionControlBonus: number // бонус в зависимости от контролирующей фракции
  }
}

interface LootEntry {
  itemId: string
  weight: number // вес в таблице лута
  minQuantity: number
  maxQuantity: number
  conditions?: LootCondition[]
}

interface LootCondition {
  type: 'player_level' | 'faction_reputation' | 'quest_completed' | 'skill_level'
  value: any
}
```

## Примеры пресетов

### Пресет "Жилой дом - Уровень 0"
- **Контейнеры**: Комоды, кухонные шкафы, тумбочки
- **Содержимое**: Консервы (60%), простые инструменты (20%), мусор (20%)
- **Количество**: 1-3 предмета

### Пресет "Военная база - Уровень 15"
- **Контейнеры**: Оружейные шкафы, военные ящики
- **Содержимое**: Оружие (40%), боеприпасы (30%), военное снаряжение (30%)
- **Количество**: 3-8 предметов

## Интеграция с генератором мира

Система пресетов будет интегрирована в генератор мира следующим образом:

1. **При генерации локации** - определяется тип и уровень опасности
2. **При размещении контейнеров** - выбирается соответствующий пресет
3. **При первом посещении** - генерируется содержимое на основе пресета
4. **При респавне лута** - используется тот же пресет с учётом времени

## Файловая структура

```
story-service/src/loot-system/
├── presets/
│   ├── level-0-presets.ts
│   ├── level-5-presets.ts
│   ├── level-10-presets.ts
│   └── level-15-presets.ts
├── generators/
│   ├── loot.generator.ts
│   └── preset.selector.ts
├── types/
│   └── loot-preset.types.ts
└── loot-system.module.ts
```

## Следующие шаги

1. Создать базовые типы для системы лута
2. Реализовать генератор лута
3. Создать пресеты для каждого уровня
4. Интегрировать с генератором локаций
5. Добавить систему респавна лута
