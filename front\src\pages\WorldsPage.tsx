import React from 'react'
import { useAuthStore } from '../store/authStore'
import WorldList from '../components/WorldList'

const WorldsPage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore()

  const handleWorldSelect = (worldId: string) => {
    console.log('Selected world:', worldId)
  }

  if (!isAuthenticated || !user) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        color: 'white',
        backgroundColor: '#1f2937'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h2>Требуется авторизация</h2>
          <p>Пожалуйста, войдите в систему для управления мирами</p>
        </div>
      </div>
    )
  }

  return (
    <div style={{ 
      minHeight: '100vh',
      backgroundColor: '#1f2937',
      padding: '2rem'
    }}>
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto',
        color: 'white'
      }}>
        <h1 style={{ 
          marginBottom: '2rem',
          textAlign: 'center',
          fontSize: '2.5rem',
          fontWeight: 'bold'
        }}>
          🌍 Управление мирами
        </h1>
        
        <p style={{ 
          textAlign: 'center',
          marginBottom: '3rem',
          color: '#9ca3af',
          fontSize: '1.1rem'
        }}>
          Добро пожаловать, {user.username}! Создавайте и управляйте своими мирами.
        </p>
        
        <WorldList onWorldSelect={handleWorldSelect} />
      </div>
    </div>
  )
}

export default WorldsPage
