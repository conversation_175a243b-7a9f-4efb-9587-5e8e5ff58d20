import { IsString, <PERSON><PERSON>ptional, <PERSON>Object, IsN<PERSON>ber } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class GenerateStoryDto {
  @ApiProperty({
    description: 'Story prompt',
    example: 'Generate a story about a survivor finding an abandoned bunker'
  })
  @IsString()
  prompt: string

  @ApiProperty({
    description: 'Additional context for story generation',
    required: false
  })
  @IsOptional()
  @IsObject()
  context?: any

  @ApiProperty({
    description: 'Maximum story length',
    example: 1000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  max_length?: number

  @ApiProperty({
    description: 'Temperature for AI generation',
    example: 0.7,
    required: false
  })
  @IsOptional()
  @IsNumber()
  temperature?: number
}

export class GenerateQuestDto {
  @ApiProperty({
    description: 'Quest type',
    example: 'exploration'
  })
  @IsString()
  quest_type: string

  @ApiProperty({
    description: 'Player level',
    example: 5
  })
  @IsNumber()
  player_level: number

  @ApiProperty({
    description: 'Location for the quest',
    example: 'Abandoned Factory'
  })
  @IsString()
  location: string

  @ApiProperty({
    description: 'Additional context',
    required: false
  })
  @IsOptional()
  @IsObject()
  context?: any
}

export class GenerateDialogueDto {
  @ApiProperty({
    description: 'Character name',
    example: 'Marcus the Trader'
  })
  @IsString()
  character_name: string

  @ApiProperty({
    description: 'Character role',
    example: 'trader'
  })
  @IsString()
  character_role: string

  @ApiProperty({
    description: 'Character personality',
    example: 'friendly, greedy, cautious'
  })
  @IsString()
  personality: string

  @ApiProperty({
    description: 'Context for dialogue',
    example: { location: 'marketplace', mood: 'neutral' }
  })
  @IsObject()
  context: any

  @ApiProperty({
    description: 'Player message',
    required: false
  })
  @IsOptional()
  @IsString()
  player_message?: string
}

export class GenerateEventDto {
  @ApiProperty({
    description: 'Event type',
    example: 'random_encounter'
  })
  @IsString()
  event_type: string

  @ApiProperty({
    description: 'Event context',
    required: false
  })
  @IsOptional()
  @IsObject()
  context?: any
}
