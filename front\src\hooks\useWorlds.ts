import { useState, useEffect, useCallback } from 'react'
import { WorldsApi, CreateWorldRequest, WorldGenerationStatus, WorldSummary, UserWorldsStats } from '../api/worldsApi'
import { World } from '@shared/types/World'

/**
 * Хук для создания мира и отслеживания прогресса
 */
export function useWorldCreation() {
  const [isCreating, setIsCreating] = useState(false)
  const [worldId, setWorldId] = useState<string | null>(null)
  const [status, setStatus] = useState<WorldGenerationStatus | null>(null)
  const [error, setError] = useState<string | null>(null)

  const createWorld = useCallback(async (params: CreateWorldRequest) => {
    try {
      setIsCreating(true)
      setError(null)
      
      const result = await WorldsApi.createWorld(params)
      setWorldId(result.worldId)
      
      // Начинаем отслеживание прогресса
      const checkProgress = async () => {
        try {
          const status = await WorldsApi.getGenerationStatus(result.worldId)
          setStatus(status)
          
          if (status.status === 'completed' || status.status === 'failed') {
            setIsCreating(false)
            if (status.status === 'failed') {
              setError(status.error || 'World generation failed')
            }
          } else {
            // Продолжаем проверять через 2 секунды
            setTimeout(checkProgress, 2000)
          }
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to check progress')
          setIsCreating(false)
        }
      }
      
      // Начинаем проверку через 1 секунду
      setTimeout(checkProgress, 1000)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create world')
      setIsCreating(false)
    }
  }, [])

  const reset = useCallback(() => {
    setIsCreating(false)
    setWorldId(null)
    setStatus(null)
    setError(null)
  }, [])

  return {
    createWorld,
    isCreating,
    worldId,
    status,
    error,
    reset,
    isCompleted: status?.status === 'completed',
    isFailed: status?.status === 'failed'
  }
}

/**
 * Хук для загрузки списка миров пользователя
 */
export function useUserWorlds(userId: string, status: 'active' | 'archived' | 'all' = 'active') {
  const [worlds, setWorlds] = useState<WorldSummary[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  })

  const loadWorlds = useCallback(async (page: number = 1, limit: number = 10) => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await WorldsApi.getUserWorlds(userId, page, limit, status)
      setWorlds(result.worlds)
      setPagination(result.pagination)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load worlds')
    } finally {
      setLoading(false)
    }
  }, [userId, status])

  useEffect(() => {
    if (userId) {
      loadWorlds()
    }
  }, [userId, status, loadWorlds])

  const refresh = useCallback(() => {
    loadWorlds(pagination.page, pagination.limit)
  }, [loadWorlds, pagination.page, pagination.limit])

  const loadPage = useCallback((page: number) => {
    loadWorlds(page, pagination.limit)
  }, [loadWorlds, pagination.limit])

  return {
    worlds,
    loading,
    error,
    pagination,
    refresh,
    loadPage
  }
}

/**
 * Хук для загрузки конкретного мира
 */
export function useWorld(worldId: string | null, userId?: string) {
  const [world, setWorld] = useState<World | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadWorld = useCallback(async () => {
    if (!worldId) return

    try {
      setLoading(true)
      setError(null)
      
      const worldData = await WorldsApi.getWorld(worldId, userId)
      setWorld(worldData)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load world')
    } finally {
      setLoading(false)
    }
  }, [worldId, userId])

  useEffect(() => {
    loadWorld()
  }, [loadWorld])

  const refresh = useCallback(() => {
    loadWorld()
  }, [loadWorld])

  return {
    world,
    loading,
    error,
    refresh
  }
}

/**
 * Хук для управления мирами (обновление, удаление, архивирование)
 */
export function useWorldManagement() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateWorld = useCallback(async (
    worldId: string,
    userId: string,
    updates: {
      name?: string
      description?: string
      isPublic?: boolean
      status?: 'active' | 'archived'
    }
  ) => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await WorldsApi.updateWorld(worldId, userId, updates)
      return result.world
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update world')
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const archiveWorld = useCallback(async (worldId: string, userId: string) => {
    try {
      setLoading(true)
      setError(null)
      
      await WorldsApi.archiveWorld(worldId, userId)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to archive world')
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteWorld = useCallback(async (worldId: string, userId: string) => {
    try {
      setLoading(true)
      setError(null)
      
      await WorldsApi.deleteWorld(worldId, userId)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete world')
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    updateWorld,
    archiveWorld,
    deleteWorld,
    loading,
    error
  }
}

/**
 * Хук для статистики миров пользователя
 */
export function useUserWorldsStats(userId: string) {
  const [stats, setStats] = useState<UserWorldsStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadStats = useCallback(async () => {
    if (!userId) return

    try {
      setLoading(true)
      setError(null)
      
      const statsData = await WorldsApi.getUserWorldsStats(userId)
      setStats(statsData)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load stats')
    } finally {
      setLoading(false)
    }
  }, [userId])

  useEffect(() => {
    loadStats()
  }, [loadStats])

  return {
    stats,
    loading,
    error,
    refresh: loadStats
  }
}

/**
 * Хук для поиска миров
 */
export function useWorldSearch() {
  const [results, setResults] = useState<WorldSummary[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const searchBySeed = useCallback(async (seed: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await WorldsApi.findWorldsBySeed(seed)
      setResults(result.worlds)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search worlds')
    } finally {
      setLoading(false)
    }
  }, [])

  const searchPublic = useCallback(async (search: string, page: number = 1) => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await WorldsApi.getPublicWorlds(page, 10, search)
      setResults(result.worlds)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search public worlds')
    } finally {
      setLoading(false)
    }
  }, [])

  const clear = useCallback(() => {
    setResults([])
    setError(null)
  }, [])

  return {
    results,
    loading,
    error,
    searchBySeed,
    searchPublic,
    clear
  }
}
