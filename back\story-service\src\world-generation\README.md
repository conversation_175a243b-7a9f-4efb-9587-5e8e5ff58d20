# Генератор мира - NuclearStory

Модуль для процедурной генерации игровых миров в постапокалиптической вселенной NuclearStory.

## Обзор

Генератор мира создает полноценные игровые миры с:
- Процедурно сгенерированной картой местности
- Локациями различных типов
- Фракциями с уникальными характеристиками
- NPC с индивидуальными особенностями
- Интеграцией с AI для создания уникального контента

## Архитектура

### Основные компоненты

```
world-generation/
├── types/                    # Типы и интерфейсы
│   └── world-generation.types.ts
├── dto/                      # DTO для API
│   └── world-generation.dto.ts
├── world.generator.ts        # Основной генератор
├── location.generator.ts     # Генератор локаций
├── faction.generator.ts      # Генератор фракций
├── npc.generator.ts         # Генератор NPC
├── map.generator.ts         # Генератор карты
├── ai.helper.ts             # Интеграция с AI
├── world-generation.service.ts    # Сервис управления
├── world-generation.controller.ts # API контроллер
└── world-generation.module.ts     # NestJS модуль
```

### Процесс генерации

1. **Инициализация** - Создание базовой структуры мира
2. **Генерация карты** - Создание сетки MapCell[][] с различными алгоритмами
3. **Генерация фракций** - Создание игровых фракций с отношениями
4. **Генерация локаций** - Размещение локаций на карте
5. **Генерация NPC** - Создание персонажей для локаций
6. **Финализация** - Установка связей и валидация

## Использование

### API Endpoints

#### Создание мира
```http
POST /world-generation/create
Content-Type: application/json

{
  "seed": "nuclear_wasteland_2077",
  "userId": "user_123",
  "worldWidth": 100,
  "worldHeight": 100,
  "algorithm": "hybrid",
  "difficulty": "normal",
  "locationDensity": 50,
  "npcDensity": 40,
  "dangerLevel": 45,
  "globalRadiation": 25,
  "radiationZones": true,
  "generateWithAI": true
}
```

#### Получение статуса генерации
```http
GET /world-generation/status/{worldId}
```

#### Получение сгенерированного мира
```http
GET /world-generation/world/{worldId}
```

### Программное использование

```typescript
import { WorldGenerationService } from './world-generation.service'

// Создание мира
const result = await worldGenerationService.createWorld({
  seed: 'my_world_seed',
  userId: 'user_123',
  worldWidth: 100,
  worldHeight: 100,
  algorithm: 'hybrid',
  difficulty: 'normal',
  // ... другие параметры
})

// Получение мира
const world = await worldGenerationService.getWorld(result.worldId)
```

## Алгоритмы генерации карты

### Perlin Noise
- Создает естественные ландшафты
- Хорошо подходит для гор и холмов
- Плавные переходы между биомами

### Cellular Automata
- Создает пещеры и сложные структуры
- Органичные формы
- Хорошо для подземелий

### Voronoi Diagrams
- Создает четкие регионы
- Подходит для территорий фракций
- Ясные границы

### Hybrid
- Комбинирует несколько алгоритмов
- Наиболее разнообразные результаты
- Рекомендуется для большинства случаев

## Система фракций

### Типы фракций
- **Выжившие** - Нейтральная фракция обычных людей
- **Военные** - Остатки довоенной армии
- **Торговцы** - Караванщики и торговцы
- **Рейдеры** - Агрессивные мародеры
- **Культисты** - Религиозные секты

### Отношения между фракциями
- Автоматически генерируются на основе идеологий
- Влияют на торговлю и квесты
- Могут изменяться в ходе игры

## Система локаций

### Типы локаций
- **Поселения** - Безопасные зоны с торговцами
- **Военные базы** - Опасные зоны с ценным лутом
- **Руины** - Заброшенные здания для исследования
- **Бункеры** - Укрепленные убежища
- **Пустоши** - Открытые опасные территории

### Характеристики локаций
- Уровень опасности (0-100)
- Уровень радиации
- Доступные ресурсы
- Наличие воды/электричества
- Контролирующая фракция

## Система NPC

### Типы NPC
- **Гражданские** - Обычные жители
- **Торговцы** - Продают товары
- **Охранники** - Защищают локации
- **Солдаты** - Военные персонажи
- **Ученые** - Исследователи
- **Мародеры** - Враждебные NPC

### Характеристики NPC
- SPECIAL атрибуты (Сила, Восприятие, и т.д.)
- Навыки и умения
- Принадлежность к фракции
- Личность и поведение
- Инвентарь и экипировка

## Интеграция с AI

### Возможности AI
- Улучшение описаний локаций
- Генерация истории фракций
- Создание диалогов для NPC
- Генерация квестов
- Создание мировых событий

### Настройка AI
```typescript
// Включение AI генерации
const params = {
  generateWithAI: true,
  aiPromptContext: 'Focus on survival and exploration themes'
}
```

## Конфигурация

### Настройки генератора
```typescript
interface GeneratorConfig {
  maxGenerationTime: number      // Максимальное время генерации
  maxLocationsPerWorld: number   // Максимум локаций
  maxNPCsPerWorld: number       // Максимум NPC
  maxFactionsPerWorld: number   // Максимум фракций
  aiTimeout: number             // Таймаут AI запросов
  enableCaching: boolean        // Включить кэширование
}
```

### Предустановки мира
- **Классическая пустошь** - Сбалансированный мир для новичков
- **Суровое выживание** - Сложный мир с высокой опасностью
- **Мирное исследование** - Спокойный мир для изучения

## Производительность

### Оптимизации
- Асинхронная генерация
- Кэширование результатов
- Ленивая загрузка чанков
- Пакетная обработка

### Мониторинг
- Время генерации
- Использование памяти
- Статистика генерации
- Логирование ошибок

## Расширение

### Добавление новых типов локаций
1. Обновить enum `LocationType`
2. Добавить логику в `LocationGenerator`
3. Создать шаблоны для AI

### Добавление новых алгоритмов карты
1. Реализовать метод в `MapGenerator`
2. Добавить в enum алгоритмов
3. Обновить документацию

### Интеграция с новыми AI сервисами
1. Расширить `AIHelper`
2. Добавить новые промпты
3. Обновить конфигурацию

## Тестирование

### Юнит-тесты
```bash
npm test world-generation
```

### Интеграционные тесты
```bash
npm test:e2e world-generation
```

### Тестирование генерации
```typescript
// Тест генерации мира
const result = await worldGenerator.generateWorld(testParams)
expect(result.success).toBe(true)
expect(result.world.locations).toBeDefined()
```

## Troubleshooting

### Частые проблемы
1. **Медленная генерация** - Уменьшить размер мира или отключить AI
2. **Ошибки AI** - Проверить доступность AI сервиса
3. **Нехватка памяти** - Уменьшить плотность объектов

### Логирование
```typescript
// Включить детальное логирование
process.env.LOG_LEVEL = 'debug'
```

## Roadmap

### Планируемые функции
- [ ] Генерация подземелий
- [ ] Динамические события
- [ ] Сезонные изменения
- [ ] Экономическая симуляция
- [ ] Расширенная AI интеграция
