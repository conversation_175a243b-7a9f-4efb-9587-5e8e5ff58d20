import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm'
import { World } from '@shared/types/World'

/**
 * Сущность для хранения сгенерированных миров
 * Хранит полный мир как JSONB объект в PostgreSQL
 */
@Entity('worlds')
@Index(['userId', 'createdAt'])
@Index(['seed'])
export class WorldEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * ID пользователя, создавшего мир
   */
  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string

  /**
   * Seed для генерации мира (для воспроизводимости)
   */
  @Column({ type: 'varchar', length: 255 })
  seed: string

  /**
   * Название мира
   */
  @Column({ type: 'varchar', length: 255 })
  name: string

  /**
   * Описание мира
   */
  @Column({ type: 'text', nullable: true })
  description: string

  /**
   * Полные данные мира в формате JSONB
   * Содержит весь объект World со всеми локациями, NPC, фракциями и картой
   */
  @Column({ type: 'jsonb' })
  worldData: World

  /**
   * Метаданные для быстрого поиска и фильтрации
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    // Размер мира
    worldSize: {
      width: number
      height: number
    }
    
    // Настройки генерации
    difficulty: string
    algorithm: string
    
    // Статистика
    stats: {
      locationsCount: number
      npcsCount: number
      factionsCount: number
      cellsCount: number
    }
    
    // Теги для поиска
    tags: string[]
    
    // Версия генератора
    generatorVersion: string
    
    // Время генерации
    generationTime: number
    
    // Использовался ли AI
    generatedWithAI: boolean
  }

  /**
   * Статус мира
   */
  @Column({ 
    type: 'enum', 
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  })
  status: 'active' | 'archived' | 'deleted'

  /**
   * Публичный ли мир (можно ли делиться)
   */
  @Column({ type: 'boolean', default: false })
  isPublic: boolean

  /**
   * Количество раз, когда мир был загружен
   */
  @Column({ type: 'integer', default: 0 })
  loadCount: number

  /**
   * Последний раз когда мир был загружен
   */
  @Column({ type: 'timestamp', nullable: true })
  lastLoadedAt: Date

  /**
   * Размер мира в байтах (для мониторинга)
   */
  @Column({ type: 'integer', nullable: true })
  sizeBytes: number

  /**
   * Версия мира (для миграций)
   */
  @Column({ type: 'varchar', length: 10, default: '1.0.0' })
  version: string

  /**
   * Дата создания
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * Дата последнего обновления
   */
  @UpdateDateColumn()
  updatedAt: Date

  /**
   * Вычисляет размер мира в байтах
   */
  calculateSize(): number {
    return JSON.stringify(this.worldData).length
  }

  /**
   * Обновляет метаданные на основе данных мира
   */
  updateMetadata(): void {
    if (!this.worldData) return

    this.metadata = {
      worldSize: this.worldData.settings.worldSize,
      difficulty: this.worldData.settings.difficulty,
      algorithm: this.worldData.settings.generationAlgorithm,
      stats: {
        locationsCount: Object.keys(this.worldData.locations || {}).length,
        npcsCount: Object.keys(this.worldData.npcs || {}).length,
        factionsCount: Object.keys(this.worldData.npcs || {}).filter(id => 
          this.worldData.npcs[id].factionId
        ).length,
        cellsCount: this.worldData.settings.worldSize.width * this.worldData.settings.worldSize.height
      },
      tags: this.worldData.metadata.tags || [],
      generatorVersion: '1.0.0',
      generationTime: 0, // Будет заполнено при сохранении
      generatedWithAI: false // Будет заполнено при сохранении
    }

    this.sizeBytes = this.calculateSize()
  }

  /**
   * Создает краткую информацию о мире (без полных данных)
   */
  toSummary() {
    return {
      id: this.id,
      userId: this.userId,
      seed: this.seed,
      name: this.name,
      description: this.description,
      metadata: this.metadata,
      status: this.status,
      isPublic: this.isPublic,
      loadCount: this.loadCount,
      lastLoadedAt: this.lastLoadedAt,
      sizeBytes: this.sizeBytes,
      version: this.version,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }
}
