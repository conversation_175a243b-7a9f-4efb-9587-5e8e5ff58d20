# Тестирование создания миров - NuclearStory

## 🚀 Быстрый тест

### 1. Перезапуск Docker
```bash
docker-compose down
docker-compose up --build
```

### 2. Проверка сервисов
Убедись, что все сервисы запущены:
- Frontend: http://localhost:3000
- Story Service: http://localhost:3003
- Save Service: http://localhost:3004
- Auth Service: http://localhost:3001

### 3. Тестирование через браузер

#### Шаг 1: Авторизация
1. Открой http://localhost:3000
2. Зарегистрируйся или войди в систему
3. Перейди в главное меню

#### Шаг 2: Создание мира
1. В главном меню нажми "Играть"
2. Нажми "Создать новый мир"
3. Заполни форму:
   - **Название**: "Тестовый мир"
   - **Seed**: "test_world_123" (или оставь пустым)
   - **Описание**: "Мой первый мир"
4. Нажми "🌍 Создать мир на сервере"

#### Шаг 3: Отслеживание прогресса
Должен появиться прогресс-бар с этапами:
- Отправка запроса на создание мира...
- Мир создается на сервере...
- Генерация мира...
- Создание локаций...
- Создание NPC...
- Сохранение мира в базу данных...
- Мир успешно создан!

### 4. Альтернативный тест через /worlds
1. Перейди на http://localhost:3000/worlds
2. Повтори создание мира

## 🔍 Проверка API напрямую

### Тест создания мира
```bash
curl -X POST http://localhost/api/world-generation/create \
  -H "Content-Type: application/json" \
  -d '{
    "seed": "test_api_world",
    "userId": "test_user_123",
    "worldWidth": 100,
    "worldHeight": 100,
    "algorithm": "hybrid",
    "difficulty": "normal",
    "locationDensity": 50,
    "npcDensity": 40,
    "resourceAbundance": 60,
    "dangerLevel": 45,
    "globalRadiation": 25,
    "radiationZones": true,
    "weatherSystem": true,
    "seasonalChanges": false,
    "dynamicEconomy": true,
    "factionWars": true,
    "generateWithAI": true,
    "timeScale": 1.0,
    "dayDuration": 24
  }'
```

### Проверка статуса
```bash
# Замени WORLD_ID на полученный ID
curl http://localhost/api/world-generation/status/WORLD_ID
```

### Получение мира
```bash
# Замени WORLD_ID на полученный ID
curl http://localhost/api/world-generation/world/WORLD_ID
```

## 🐛 Отладка проблем

### Проблема 1: 404 Not Found
**Причина**: Nginx не проксирует запросы
**Решение**: Проверь nginx.conf и перезапусти контейнеры

### Проблема 2: 500 Internal Server Error
**Причина**: Ошибка в story-service
**Решение**: Проверь логи:
```bash
docker logs nuclearstory-story-service-1
```

### Проблема 3: Мир не сохраняется в БД
**Причина**: save-service не запущен или нет подключения к БД
**Решение**: 
1. Проверь логи save-service
2. Проверь подключение к PostgreSQL через Adminer

### Проблема 4: Прогресс застрял
**Причина**: Ошибка в генерации или сохранении
**Решение**: Проверь логи обоих сервисов

## 📊 Проверка в базе данных

### Через Adminer
1. Открой http://localhost:8080
2. Подключись к `postgres-saves`:
   - Server: `postgres-saves`
   - Username: `nuclearstory`
   - Password: `password`
   - Database: `saves_db`
3. Проверь таблицу `worlds`

### Через SQL
```sql
-- Проверить созданные миры
SELECT id, name, seed, user_id, created_at, size_bytes 
FROM worlds 
ORDER BY created_at DESC;

-- Проверить метаданные мира
SELECT id, name, metadata 
FROM worlds 
WHERE name = 'Тестовый мир';
```

## ✅ Ожидаемый результат

После успешного создания мира:

1. **В интерфейсе**:
   - Прогресс-бар достигает 100%
   - Появляется сообщение "Мир успешно создан!"
   - Мир добавляется в список миров

2. **В базе данных**:
   - Новая запись в таблице `worlds`
   - Поле `world_data` содержит JSONB с полным миром
   - Поле `metadata` содержит статистику

3. **В логах**:
   - story-service: "World generation completed"
   - save-service: "World saved to database"

## 🔧 Настройка для разработки

### Включение подробных логов
В docker-compose.yml добавь:
```yaml
story-service:
  environment:
    - LOG_LEVEL=debug
    - NODE_ENV=development

save-service:
  environment:
    - LOG_LEVEL=debug
    - NODE_ENV=development
```

### Отключение AI для быстрого тестирования
В форме создания мира сними галочку "Использовать AI для генерации"

### Уменьшение размера мира
Установи размер 50x50 вместо 100x100 для быстрой генерации
