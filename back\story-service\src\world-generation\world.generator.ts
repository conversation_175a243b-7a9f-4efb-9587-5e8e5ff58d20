import { Injectable, Logger } from '@nestjs/common'
import { World, WorldSettings } from '@shared/types/World'
import { 
  WorldGenerationParams, 
  WorldGenerationResult, 
  GenerationProgress,
  GeneratorConfig 
} from './types/world-generation.types'
import { LocationGenerator } from './location.generator'
import { FactionGenerator } from './faction.generator'
import { NPCGenerator } from './npc.generator'
import { MapGenerator } from './map.generator'
import { AIHelper } from './ai.helper'

/**
 * Основной генератор мира
 * Координирует работу всех подгенераторов и создает полный мир
 */
@Injectable()
export class WorldGenerator {
  private readonly logger = new Logger(WorldGenerator.name)
  
  constructor(
    private readonly locationGenerator: LocationGenerator,
    private readonly factionGenerator: FactionGenerator,
    private readonly npcGenerator: NPCGenerator,
    private readonly mapGenerator: MapGenerator,
    private readonly aiHelper: <PERSON><PERSON>el<PERSON>,
  ) {}

  /**
   * Генерирует новый мир
   */
  async generateWorld(params: WorldGenerationParams): Promise<WorldGenerationResult> {
    const startTime = Date.now()
    
    try {
      this.logger.log(`Starting world generation with seed: ${params.seed}`)
      
      // Инициализация
      const world = this.initializeWorld(params)
      
      // Этап 1: Генерация карты мира (сетка MapCell[][])
      this.logger.log('Generating world map...')
      world.worldGrid = await this.mapGenerator.generateMap({
        seed: params.seed,
        width: params.worldSize.width,
        height: params.worldSize.height,
        algorithm: params.settings.generationAlgorithm,
        settings: {
          landRatio: 0.7,
          mountainDensity: 0.3,
          forestDensity: 0.4,
          radiationZones: params.settings.radiationZones,
          globalRadiation: params.settings.globalRadiation
        }
      })

      // Этап 2: Генерация фракций
      this.logger.log('Generating factions...')
      const factions = await this.factionGenerator.generateFactions({
        worldSeed: params.seed,
        worldSettings: params.settings,
        existingFactions: [],
        maxFactions: this.calculateMaxFactions(params.settings),
        generateWithAI: params.generateWithAI
      })
      
      // Добавляем фракции в мир
      factions.forEach(faction => {
        world.npcs[faction.id] = faction
      })

      // Этап 3: Генерация локаций
      this.logger.log('Generating locations...')
      const locations = await this.locationGenerator.generateLocations({
        worldSeed: params.seed,
        worldSize: params.worldSize,
        locationDensity: params.settings.locationDensity,
        dangerLevel: params.settings.dangerLevel,
        existingLocations: []
      })
      
      // Добавляем локации в мир
      locations.forEach(location => {
        world.locations[location.id] = location
      })

      // Этап 4: Генерация NPC
      this.logger.log('Generating NPCs...')
      const npcs = await this.npcGenerator.generateNPCs({
        worldSeed: params.seed,
        locations: locations,
        factions: factions,
        npcDensity: params.settings.npcDensity,
        generateWithAI: params.generateWithAI
      })
      
      // Добавляем NPC в мир
      npcs.forEach(npc => {
        world.npcs[npc.id] = npc
      })

      // Этап 5: Финализация
      this.logger.log('Finalizing world generation...')
      this.finalizeWorld(world)

      const generationTime = Date.now() - startTime
      
      this.logger.log(`World generation completed in ${generationTime}ms`)
      
      return {
        success: true,
        world,
        generationTime,
        stats: {
          locationsGenerated: locations.length,
          npcsGenerated: npcs.length,
          factionsGenerated: factions.length,
          cellsGenerated: params.worldSize.width * params.worldSize.height
        }
      }
      
    } catch (error) {
      this.logger.error('World generation failed', error)
      
      return {
        success: false,
        error: error.message,
        generationTime: Date.now() - startTime,
        stats: {
          locationsGenerated: 0,
          npcsGenerated: 0,
          factionsGenerated: 0,
          cellsGenerated: 0
        }
      }
    }
  }

  /**
   * Инициализирует базовую структуру мира
   */
  private initializeWorld(params: WorldGenerationParams): World {
    const worldId = this.generateWorldId(params.seed, params.userId)
    
    return {
      id: worldId,
      metadata: {
        name: `Мир ${params.seed}`,
        description: 'Сгенерированный мир NuclearStory',
        version: '1.0.0',
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        createdBy: params.userId,
        tags: ['generated', 'post-apocalyptic'],
        isPublic: false,
        saveCompatibility: '1.0.0'
      },
      settings: params.settings,
      state: {
        currentTime: 0,
        daysPassed: 0,
        gameSpeed: 1.0,
        isPaused: false,
        weather: {
          current: 'clear',
          temperature: 20,
          humidity: 50,
          windSpeed: 5,
          radiation: params.settings.globalRadiation
        },
        globalEvents: [],
        economicState: {
          inflation: 1.0,
          tradeRoutes: [],
          marketPrices: {}
        }
      },
      worldGrid: [], // Будет заполнено генератором карты
      locations: {},
      npcs: {},
      questSystem: {
        availableQuests: [],
        completedQuests: [],
        questTemplates: []
      },
      eventSystem: {
        scheduledEvents: [],
        recurringEvents: []
      },
      generators: {
        locationGenerator: {
          templates: [],
          lastGenerated: { x: 0, y: 0 },
          generationQueue: []
        },
        npcGenerator: {
          templates: [],
          spawnPoints: [],
          maxNPCs: this.calculateMaxNPCs(params.settings)
        },
        questGenerator: {
          templates: [],
          difficulty: params.settings.dangerLevel,
          themes: ['survival', 'exploration', 'faction']
        }
      },
      cache: {
        loadedChunks: new Set(),
        activeRegions: new Set(),
        nearbyLocations: {}
      }
    }
  }

  /**
   * Генерирует уникальный ID мира
   */
  private generateWorldId(seed: string, userId: string): string {
    const timestamp = Date.now()
    return `world_${userId}_${seed}_${timestamp}`
  }

  /**
   * Вычисляет максимальное количество фракций
   */
  private calculateMaxFactions(settings: WorldSettings): number {
    const baseCount = 3
    const difficultyMultiplier = settings.difficulty === 'easy' ? 0.8 : 
                                 settings.difficulty === 'hard' ? 1.2 : 
                                 settings.difficulty === 'nightmare' ? 1.5 : 1.0
    
    return Math.floor(baseCount * difficultyMultiplier)
  }

  /**
   * Вычисляет максимальное количество NPC
   */
  private calculateMaxNPCs(settings: WorldSettings): number {
    const worldArea = settings.worldSize.width * settings.worldSize.height
    const baseNPCsPerCell = 0.1
    const densityMultiplier = settings.npcDensity / 100
    
    return Math.floor(worldArea * baseNPCsPerCell * densityMultiplier)
  }

  /**
   * Финализирует генерацию мира
   */
  private finalizeWorld(world: World): void {
    // Обновляем связи между объектами
    this.updateWorldConnections(world)
    
    // Устанавливаем начальные состояния
    this.setInitialStates(world)
    
    // Валидируем мир
    this.validateWorld(world)
  }

  /**
   * Обновляет связи между объектами мира
   */
  private updateWorldConnections(world: World): void {
    // Связываем локации с ячейками карты
    Object.values(world.locations).forEach(location => {
      const cell = world.worldGrid[location.position.y]?.[location.position.x]
      if (cell) {
        cell.hasLocation = true
        cell.locationId = location.id
      }
    })

    // Связываем NPC с локациями
    Object.values(world.npcs).forEach(npc => {
      if (npc.currentLocationId && world.locations[npc.currentLocationId]) {
        world.locations[npc.currentLocationId].npcs.push(npc.id)
      }
    })
  }

  /**
   * Устанавливает начальные состояния
   */
  private setInitialStates(world: World): void {
    // Устанавливаем начальное время
    world.state.currentTime = 0
    world.state.daysPassed = 0
    
    // Инициализируем погоду
    world.state.weather.current = 'clear'
    world.state.weather.temperature = 20
  }

  /**
   * Валидирует сгенерированный мир
   */
  private validateWorld(world: World): void {
    if (!world.worldGrid || world.worldGrid.length === 0) {
      throw new Error('World grid is empty')
    }
    
    if (Object.keys(world.locations).length === 0) {
      this.logger.warn('No locations generated')
    }
    
    if (Object.keys(world.npcs).length === 0) {
      this.logger.warn('No NPCs generated')
    }
  }
}
