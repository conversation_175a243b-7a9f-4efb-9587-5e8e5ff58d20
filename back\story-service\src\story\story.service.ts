import { Injectable, Logger } from '@nestjs/common'
import { AiService } from '../ai/ai.service'
import { GenerateStoryDto, GenerateQuestDto, GenerateDialogueDto, GenerateEventDto } from './dto/story.dto'

@Injectable()
export class StoryService {
  private readonly logger = new Logger(StoryService.name)

  constructor(private readonly aiService: AiService) {}

  async generateStory(generateStoryDto: GenerateStoryDto) {
    this.logger.log('Generating story content')
    
    const result = await this.aiService.generateStory({
      prompt: generateStoryDto.prompt,
      context: generateStoryDto.context,
      max_length: generateStoryDto.max_length,
      temperature: generateStoryDto.temperature
    })

    return {
      success: result.success,
      content: result.content,
      error: result.error
    }
  }

  async generateQuest(generateQuestDto: GenerateQuestDto) {
    this.logger.log('Generating quest content')
    
    const result = await this.aiService.generateQuest(
      generateQuestDto.quest_type,
      generateQuestDto.player_level,
      generateQuestDto.location,
      generateQuestDto.context
    )

    return {
      success: result.success,
      content: result.content,
      error: result.error
    }
  }

  async generateDialogue(generateDialogueDto: GenerateDialogueDto) {
    this.logger.log('Generating dialogue content')
    
    const result = await this.aiService.generateDialogue(
      generateDialogueDto.character_name,
      generateDialogueDto.character_role,
      generateDialogueDto.personality,
      generateDialogueDto.context,
      generateDialogueDto.player_message
    )

    return {
      success: result.success,
      content: result.content,
      error: result.error
    }
  }

  async generateEvent(generateEventDto: GenerateEventDto) {
    this.logger.log('Generating event content')
    
    const result = await this.aiService.generateEvent(
      generateEventDto.event_type,
      generateEventDto.context
    )

    return {
      success: result.success,
      content: result.content,
      error: result.error
    }
  }

  async getTemplates(type: string) {
    this.logger.log(`Getting templates for type: ${type}`)
    
    // Простые шаблоны для примера
    const templates = {
      story: [
        {
          id: 'survival',
          name: 'Survival Story',
          prompt: 'A survivor faces a dangerous situation in the wasteland...'
        },
        {
          id: 'discovery',
          name: 'Discovery Story', 
          prompt: 'A character discovers something unexpected...'
        }
      ],
      quest: [
        {
          id: 'fetch',
          name: 'Fetch Quest',
          description: 'Retrieve an item from a dangerous location'
        },
        {
          id: 'escort',
          name: 'Escort Quest',
          description: 'Safely escort someone to their destination'
        }
      ]
    }

    return {
      success: true,
      templates: templates[type] || [],
      type
    }
  }
}
