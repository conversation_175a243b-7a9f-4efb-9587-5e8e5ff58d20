import { Injectable, Logger } from '@nestjs/common'
import { NPC, NPCType } from '@shared/types/NPC'
import { Location } from '@shared/types/Location'
import { Faction } from '@shared/types/Faction'
import { NPCGenerationParams } from './types/world-generation.types'
import { AIHelper } from './ai.helper'

/**
 * Генератор NPC
 * Создает NPC для локаций с учетом фракций и AI
 */
@Injectable()
export class NPCGenerator {
  private readonly logger = new Logger(NPCGenerator.name)
  
  constructor(private readonly aiHelper: AIHelper) {}

  /**
   * Генерирует NPC для мира
   */
  async generateNPCs(params: NPCGenerationParams): Promise<NPC[]> {
    this.logger.log('Starting NPC generation...')
    
    const npcs: NPC[] = []
    
    // Генерируем NPC для каждой локации
    for (const location of params.locations) {
      const locationNPCs = await this.generateNPCsForLocation(location, params)
      npcs.push(...locationNPCs)
    }
    
    // Генерируем бродячих NPC
    const wanderingNPCs = await this.generateWanderingNPCs(params)
    npcs.push(...wanderingNPCs)
    
    this.logger.log(`Generated ${npcs.length} NPCs`)
    return npcs
  }

  /**
   * Генерирует NPC для конкретной локации
   */
  private async generateNPCsForLocation(location: Location, params: NPCGenerationParams): Promise<NPC[]> {
    const npcs: NPC[] = []
    const npcCount = this.calculateNPCCountForLocation(location, params.npcDensity)
    
    for (let i = 0; i < npcCount; i++) {
      try {
        const npc = await this.generateSingleNPC(location, params, i)
        npcs.push(npc)
      } catch (error) {
        this.logger.warn(`Failed to generate NPC ${i} for location ${location.id}: ${error.message}`)
      }
    }
    
    return npcs
  }

  /**
   * Вычисляет количество NPC для локации
   */
  private calculateNPCCountForLocation(location: Location, npcDensity: number): number {
    const baseCount = this.getBaseNPCCountByLocationType(location.type)
    const densityMultiplier = npcDensity / 100
    const safetyMultiplier = location.safeZone ? 1.5 : 0.7
    
    return Math.floor(baseCount * densityMultiplier * safetyMultiplier)
  }

  /**
   * Получает базовое количество NPC по типу локации
   */
  private getBaseNPCCountByLocationType(locationType: string): number {
    const npcCounts = {
      'settlement': 8,
      'military_base': 6,
      'bunker': 4,
      'shelter': 3,
      'factory': 2,
      'ruins': 1,
      'wasteland': 0
    }
    
    return npcCounts[locationType] || 2
  }

  /**
   * Генерирует одного NPC
   */
  private async generateSingleNPC(location: Location, params: NPCGenerationParams, index: number): Promise<NPC> {
    const npcId = `npc_${location.id}_${index}`
    
    // Определяем тип NPC на основе локации
    const npcType = this.selectNPCType(location)
    
    // Выбираем фракцию для NPC
    const faction = this.selectFactionForNPC(location, params.factions)
    
    // Создаем базового NPC
    const baseNPC = this.createBaseNPC(npcId, npcType, location, faction)
    
    // Улучшаем с помощью AI если включено
    if (params.generateWithAI) {
      return await this.enhanceNPCWithAI(baseNPC, location, faction)
    }
    
    return baseNPC
  }

  /**
   * Выбирает тип NPC на основе локации
   */
  private selectNPCType(location: Location): NPCType {
    const locationTypeToNPCType = {
      'settlement': [NPCType.CIVILIAN, NPCType.TRADER, NPCType.GUARD],
      'military_base': [NPCType.SOLDIER, NPCType.OFFICER, NPCType.GUARD],
      'bunker': [NPCType.CIVILIAN, NPCType.SCIENTIST, NPCType.GUARD],
      'shelter': [NPCType.CIVILIAN, NPCType.REFUGEE],
      'factory': [NPCType.WORKER, NPCType.ENGINEER],
      'ruins': [NPCType.SCAVENGER, NPCType.RAIDER],
      'wasteland': [NPCType.WANDERER, NPCType.RAIDER]
    }
    
    const possibleTypes = locationTypeToNPCType[location.type] || [NPCType.CIVILIAN]
    return possibleTypes[Math.floor(Math.random() * possibleTypes.length)]
  }

  /**
   * Выбирает фракцию для NPC
   */
  private selectFactionForNPC(location: Location, factions: Faction[]): Faction | undefined {
    // Если локация контролируется фракцией
    if (location.factionControlled) {
      return factions.find(f => f.id === location.factionControlled)
    }
    
    // Случайный выбор фракции с вероятностью 60%
    if (Math.random() > 0.4) {
      return factions[Math.floor(Math.random() * factions.length)]
    }
    
    return undefined
  }

  /**
   * Создает базового NPC
   */
  private createBaseNPC(id: string, type: NPCType, location: Location, faction?: Faction): NPC {
    const names = this.generateNPCName(type)
    
    return {
      id,
      name: names.name,
      description: this.generateNPCDescription(type),
      type,
      
      // Внешность
      appearance: {
        gender: Math.random() > 0.5 ? 'male' : 'female',
        age: this.generateAge(type),
        height: Math.floor(Math.random() * 40) + 160, // 160-200 см
        build: this.selectBuild(type),
        hairColor: this.selectHairColor(),
        eyeColor: this.selectEyeColor(),
        skinTone: this.selectSkinTone(),
        distinguishingMarks: this.generateDistinguishingMarks()
      },
      
      // Позиция и локация
      position: {
        x: Math.floor(Math.random() * location.size.width),
        y: Math.floor(Math.random() * location.size.height)
      },
      currentLocationId: location.id,
      
      // Характеристики
      level: this.generateLevel(type),
      health: 100,
      maxHealth: 100,
      
      // SPECIAL характеристики
      special: this.generateSPECIAL(type),
      
      // Навыки
      skills: this.generateSkills(type),
      
      // Фракция
      factionId: faction?.id,
      factionRank: faction ? this.selectFactionRank(faction) : undefined,
      reputation: faction ? 0 : undefined,
      
      // Инвентарь
      inventory: this.generateInventory(type),
      equipment: this.generateEquipment(type),
      
      // Торговля
      trade: this.generateTrade(type),
      
      // Диалоги
      dialogues: this.generateDialogues(type),
      
      // Поведение и ИИ
      personality: this.generatePersonality(type),
      
      // Расписание
      currentActivity: 'idle',
      
      // Квесты
      availableQuests: [],
      completedQuests: [],
      
      // Статус
      isAlive: true,
      isEssential: this.isEssentialNPC(type),
      isUnique: false,
      
      // Временные метки
      createdAt: new Date(),
      lastSeenAt: new Date()
    }
  }

  /**
   * Генерирует имя NPC
   */
  private generateNPCName(type: NPCType): { name: string } {
    const maleNames = ['Алекс', 'Дмитрий', 'Сергей', 'Иван', 'Михаил', 'Андрей', 'Владимир']
    const femaleNames = ['Анна', 'Мария', 'Елена', 'Ольга', 'Татьяна', 'Наталья', 'Светлана']
    const surnames = ['Смирнов', 'Иванов', 'Петров', 'Сидоров', 'Козлов', 'Новиков', 'Морозов']
    
    const isMale = Math.random() > 0.5
    const firstName = isMale ? 
      maleNames[Math.floor(Math.random() * maleNames.length)] :
      femaleNames[Math.floor(Math.random() * femaleNames.length)]
    
    const lastName = surnames[Math.floor(Math.random() * surnames.length)]
    
    return { name: `${firstName} ${lastName}` }
  }

  /**
   * Генерирует описание NPC
   */
  private generateNPCDescription(type: NPCType): string {
    const descriptions = {
      [NPCType.CIVILIAN]: 'Обычный житель пустоши, пытающийся выжить в суровом мире.',
      [NPCType.TRADER]: 'Опытный торговец, знающий цену каждой вещи.',
      [NPCType.GUARD]: 'Охранник, защищающий поселение от опасностей.',
      [NPCType.SOLDIER]: 'Военный, прошедший через множество сражений.',
      [NPCType.OFFICER]: 'Командир, ответственный за безопасность.',
      [NPCType.SCIENTIST]: 'Ученый, изучающий последствия катастрофы.',
      [NPCType.ENGINEER]: 'Инженер, поддерживающий технику в рабочем состоянии.',
      [NPCType.DOCTOR]: 'Медик, спасающий жизни в пустоши.',
      [NPCType.SCAVENGER]: 'Мусорщик, ищущий полезные вещи в руинах.',
      [NPCType.RAIDER]: 'Агрессивный мародер, живущий грабежом.',
      [NPCType.WANDERER]: 'Странник, путешествующий по пустоши.',
      [NPCType.REFUGEE]: 'Беженец, потерявший свой дом.',
      [NPCType.WORKER]: 'Рабочий, выполняющий тяжелую работу.'
    }
    
    return descriptions[type] || 'Житель пустоши.'
  }

  // Вспомогательные методы генерации характеристик
  private generateAge(type: NPCType): number {
    const ageRanges = {
      [NPCType.CIVILIAN]: [20, 60],
      [NPCType.SOLDIER]: [20, 45],
      [NPCType.OFFICER]: [30, 55],
      [NPCType.SCIENTIST]: [25, 65],
      [NPCType.REFUGEE]: [15, 70]
    }
    
    const range = ageRanges[type] || [18, 60]
    return Math.floor(Math.random() * (range[1] - range[0])) + range[0]
  }

  private selectBuild(type: NPCType): 'thin' | 'average' | 'muscular' | 'heavy' {
    const builds = {
      [NPCType.SOLDIER]: ['muscular', 'average'],
      [NPCType.GUARD]: ['muscular', 'average'],
      [NPCType.SCIENTIST]: ['thin', 'average'],
      [NPCType.REFUGEE]: ['thin']
    }
    
    const possibleBuilds = builds[type] || ['thin', 'average', 'muscular', 'heavy']
    return possibleBuilds[Math.floor(Math.random() * possibleBuilds.length)] as any
  }

  private selectHairColor(): string {
    const colors = ['черный', 'коричневый', 'русый', 'рыжий', 'седой', 'лысый']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  private selectEyeColor(): string {
    const colors = ['карие', 'голубые', 'зеленые', 'серые']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  private selectSkinTone(): string {
    const tones = ['светлый', 'средний', 'темный', 'загорелый']
    return tones[Math.floor(Math.random() * tones.length)]
  }

  private generateDistinguishingMarks(): string[] {
    const marks = ['шрам на лице', 'татуировка', 'хромота', 'протез', 'очки']
    const count = Math.floor(Math.random() * 3) // 0-2 особенности
    const result = []
    
    for (let i = 0; i < count; i++) {
      const mark = marks[Math.floor(Math.random() * marks.length)]
      if (!result.includes(mark)) {
        result.push(mark)
      }
    }
    
    return result
  }

  private generateLevel(type: NPCType): number {
    const levelRanges = {
      [NPCType.CIVILIAN]: [1, 5],
      [NPCType.SOLDIER]: [3, 8],
      [NPCType.OFFICER]: [5, 12],
      [NPCType.SCIENTIST]: [4, 10],
      [NPCType.RAIDER]: [2, 6]
    }
    
    const range = levelRanges[type] || [1, 5]
    return Math.floor(Math.random() * (range[1] - range[0])) + range[0]
  }

  private generateSPECIAL(type: NPCType): any {
    // Базовые характеристики SPECIAL (1-10)
    const base = {
      strength: 5,
      perception: 5,
      endurance: 5,
      charisma: 5,
      intelligence: 5,
      agility: 5,
      luck: 5
    }
    
    // Модификаторы по типу NPC
    const modifiers = {
      [NPCType.SOLDIER]: { strength: 2, endurance: 2, agility: 1 },
      [NPCType.SCIENTIST]: { intelligence: 3, perception: 2 },
      [NPCType.TRADER]: { charisma: 3, intelligence: 1 },
      [NPCType.SCAVENGER]: { perception: 2, agility: 2, luck: 1 }
    }
    
    const modifier = modifiers[type] || {}
    
    Object.keys(modifier).forEach(key => {
      base[key] = Math.min(10, base[key] + modifier[key])
    })
    
    return base
  }

  private generateSkills(type: NPCType): Record<string, number> {
    const baseSkills = {
      combat: 25,
      firearms: 25,
      melee: 25,
      medicine: 15,
      science: 15,
      repair: 20,
      lockpick: 15,
      sneak: 20,
      speech: 25,
      barter: 20
    }
    
    // Бонусы по типу NPC
    const skillBonuses = {
      [NPCType.SOLDIER]: { combat: 30, firearms: 35 },
      [NPCType.SCIENTIST]: { science: 50, medicine: 30 },
      [NPCType.TRADER]: { barter: 40, speech: 35 },
      [NPCType.DOCTOR]: { medicine: 60, science: 25 }
    }
    
    const bonuses = skillBonuses[type] || {}
    
    Object.keys(bonuses).forEach(skill => {
      baseSkills[skill] += bonuses[skill]
    })
    
    return baseSkills
  }

  private generateInventory(type: NPCType): string[] {
    // Упрощенная генерация инвентаря
    return []
  }

  private generateEquipment(type: NPCType): Record<string, string> {
    // Упрощенная генерация экипировки
    return {}
  }

  private generateTrade(type: NPCType): any {
    if (type === NPCType.TRADER) {
      return {
        buyPrices: {},
        sellItems: [],
        currency: Math.floor(Math.random() * 1000) + 200,
        tradeModifier: 1.0
      }
    }
    return undefined
  }

  private generateDialogues(type: NPCType): any[] {
    // Упрощенная генерация диалогов
    return []
  }

  private generatePersonality(type: NPCType): any {
    return {
      aggression: Math.floor(Math.random() * 100),
      friendliness: Math.floor(Math.random() * 100),
      intelligence: Math.floor(Math.random() * 100),
      courage: Math.floor(Math.random() * 100),
      greed: Math.floor(Math.random() * 100)
    }
  }

  private selectFactionRank(faction: Faction): string | undefined {
    if (faction.ranks.length > 0) {
      return faction.ranks[0].id // Начальный ранг
    }
    return undefined
  }

  private isEssentialNPC(type: NPCType): boolean {
    return [NPCType.TRADER, NPCType.DOCTOR].includes(type)
  }

  /**
   * Генерирует бродячих NPC
   */
  private async generateWanderingNPCs(params: NPCGenerationParams): Promise<NPC[]> {
    const wanderingCount = Math.floor(params.locations.length * 0.1) // 10% от количества локаций
    const npcs: NPC[] = []
    
    for (let i = 0; i < wanderingCount; i++) {
      // Создаем странника без привязки к локации
      const npc = this.createBaseNPC(
        `wandering_npc_${i}`,
        NPCType.WANDERER,
        params.locations[0], // Временная локация
        undefined
      )
      
      npc.currentLocationId = '' // Бродячий NPC
      npcs.push(npc)
    }
    
    return npcs
  }

  /**
   * Улучшает NPC с помощью AI
   */
  private async enhanceNPCWithAI(npc: NPC, location: Location, faction?: Faction): Promise<NPC> {
    try {
      // Здесь будет вызов AI для улучшения описания и диалогов
      // Пока возвращаем базового NPC
      return npc
    } catch (error) {
      this.logger.warn(`Failed to enhance NPC with AI: ${error.message}`)
      return npc
    }
  }
}
