import { <PERSON>String, IsNumber, IsBoolean, IsOptional, IsEnum, IsObject, Min, Max } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

/**
 * DTO для создания мира
 */
export class CreateWorldDto {
  @ApiProperty({
    description: 'World seed for generation',
    example: 'nuclear_wasteland_2077'
  })
  @IsString()
  seed: string

  @ApiProperty({
    description: 'User ID who creates the world',
    example: 'user_123'
  })
  @IsString()
  userId: string

  @ApiProperty({
    description: 'World width in cells',
    example: 100,
    minimum: 50,
    maximum: 500
  })
  @IsNumber()
  @Min(50)
  @Max(500)
  worldWidth: number

  @ApiProperty({
    description: 'World height in cells',
    example: 100,
    minimum: 50,
    maximum: 500
  })
  @IsNumber()
  @Min(50)
  @Max(500)
  worldHeight: number

  @ApiProperty({
    description: 'Generation algorithm',
    enum: ['perlin', 'cellular', 'voronoi', 'hybrid'],
    example: 'hybrid'
  })
  @IsEnum(['perlin', 'cellular', 'voronoi', 'hybrid'])
  algorithm: 'perlin' | 'cellular' | 'voronoi' | 'hybrid'

  @ApiProperty({
    description: 'Difficulty level',
    enum: ['easy', 'normal', 'hard', 'nightmare'],
    example: 'normal'
  })
  @IsEnum(['easy', 'normal', 'hard', 'nightmare'])
  difficulty: 'easy' | 'normal' | 'hard' | 'nightmare'

  @ApiProperty({
    description: 'Location density (0-100)',
    example: 50,
    minimum: 0,
    maximum: 100
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  locationDensity: number

  @ApiProperty({
    description: 'NPC density (0-100)',
    example: 40,
    minimum: 0,
    maximum: 100
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  npcDensity: number

  @ApiProperty({
    description: 'Resource abundance (0-100)',
    example: 60,
    minimum: 0,
    maximum: 100
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  resourceAbundance: number

  @ApiProperty({
    description: 'Danger level (0-100)',
    example: 45,
    minimum: 0,
    maximum: 100
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  dangerLevel: number

  @ApiProperty({
    description: 'Global radiation level',
    example: 25,
    minimum: 0,
    maximum: 100
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  globalRadiation: number

  @ApiProperty({
    description: 'Enable radiation zones',
    example: true
  })
  @IsBoolean()
  radiationZones: boolean

  @ApiProperty({
    description: 'Enable weather system',
    example: true
  })
  @IsBoolean()
  weatherSystem: boolean

  @ApiProperty({
    description: 'Enable seasonal changes',
    example: false
  })
  @IsBoolean()
  seasonalChanges: boolean

  @ApiProperty({
    description: 'Enable dynamic economy',
    example: true
  })
  @IsBoolean()
  dynamicEconomy: boolean

  @ApiProperty({
    description: 'Enable faction wars',
    example: true
  })
  @IsBoolean()
  factionWars: boolean

  @ApiProperty({
    description: 'Use AI for generation',
    example: true
  })
  @IsBoolean()
  generateWithAI: boolean

  @ApiProperty({
    description: 'Additional context for AI generation',
    example: 'Focus on survival and exploration themes',
    required: false
  })
  @IsOptional()
  @IsString()
  aiPromptContext?: string

  @ApiProperty({
    description: 'Time scale multiplier',
    example: 1.0,
    minimum: 0.1,
    maximum: 10.0
  })
  @IsNumber()
  @Min(0.1)
  @Max(10.0)
  timeScale: number

  @ApiProperty({
    description: 'Day duration in minutes',
    example: 24,
    minimum: 1,
    maximum: 1440
  })
  @IsNumber()
  @Min(1)
  @Max(1440)
  dayDuration: number
}

/**
 * DTO для статуса генерации мира
 */
export class WorldGenerationStatusDto {
  @ApiProperty({
    description: 'World ID',
    example: 'world_user_123_nuclear_wasteland_2077_1640995200000'
  })
  @IsString()
  worldId: string

  @ApiProperty({
    description: 'Generation status',
    enum: ['pending', 'in_progress', 'completed', 'failed'],
    example: 'in_progress'
  })
  @IsEnum(['pending', 'in_progress', 'completed', 'failed'])
  status: 'pending' | 'in_progress' | 'completed' | 'failed'

  @ApiProperty({
    description: 'Generation progress (0-100)',
    example: 65,
    minimum: 0,
    maximum: 100
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  progress: number

  @ApiProperty({
    description: 'Current generation stage',
    example: 'generating_npcs'
  })
  @IsString()
  currentStage: string

  @ApiProperty({
    description: 'Current task description',
    example: 'Generating NPCs for settlements...'
  })
  @IsString()
  currentTask: string

  @ApiProperty({
    description: 'Estimated time remaining in seconds',
    example: 45,
    required: false
  })
  @IsOptional()
  @IsNumber()
  estimatedTimeRemaining?: number

  @ApiProperty({
    description: 'Error message if generation failed',
    required: false
  })
  @IsOptional()
  @IsString()
  error?: string

  @ApiProperty({
    description: 'Generation statistics',
    required: false
  })
  @IsOptional()
  @IsObject()
  stats?: {
    locationsGenerated: number
    npcsGenerated: number
    factionsGenerated: number
    cellsGenerated: number
  }

  @ApiProperty({
    description: 'Generation start time',
    example: '2023-12-31T23:59:59.000Z'
  })
  startedAt: Date

  @ApiProperty({
    description: 'Generation completion time',
    required: false
  })
  @IsOptional()
  completedAt?: Date
}

/**
 * DTO для предустановки мира
 */
export class WorldPresetDto {
  @ApiProperty({
    description: 'Preset ID',
    example: 'classic_wasteland'
  })
  @IsString()
  id: string

  @ApiProperty({
    description: 'Preset name',
    example: 'Классическая пустошь'
  })
  @IsString()
  name: string

  @ApiProperty({
    description: 'Preset description',
    example: 'Стандартный постапокалиптический мир с умеренной сложностью'
  })
  @IsString()
  description: string

  @ApiProperty({
    description: 'Preset settings',
    example: {
      difficulty: 'normal',
      locationDensity: 50,
      npcDensity: 40,
      dangerLevel: 45
    }
  })
  @IsObject()
  settings: Partial<CreateWorldDto>

  @ApiProperty({
    description: 'Preset tags',
    example: ['beginner-friendly', 'balanced', 'exploration']
  })
  tags: string[]

  @ApiProperty({
    description: 'Recommended for beginners',
    example: true
  })
  @IsBoolean()
  isRecommended: boolean
}

/**
 * DTO для шаблона генерации
 */
export class GenerationTemplateDto {
  @ApiProperty({
    description: 'Template ID',
    example: 'military_base_template'
  })
  @IsString()
  id: string

  @ApiProperty({
    description: 'Template name',
    example: 'Военная база'
  })
  @IsString()
  name: string

  @ApiProperty({
    description: 'Template description',
    example: 'Шаблон для генерации военных объектов'
  })
  @IsString()
  description: string

  @ApiProperty({
    description: 'Template type',
    enum: ['location', 'faction', 'npc', 'quest'],
    example: 'location'
  })
  @IsEnum(['location', 'faction', 'npc', 'quest'])
  type: 'location' | 'faction' | 'npc' | 'quest'

  @ApiProperty({
    description: 'Template conditions',
    example: {
      biome: ['wasteland', 'urban_ruins'],
      dangerLevel: { min: 60, max: 100 }
    }
  })
  @IsObject()
  conditions: Record<string, any>

  @ApiProperty({
    description: 'Template parameters',
    example: {
      hasWeapons: true,
      securityLevel: 'high',
      populationRange: [10, 30]
    }
  })
  @IsObject()
  parameters: Record<string, any>
}
