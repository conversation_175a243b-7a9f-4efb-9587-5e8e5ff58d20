import { Injectable, Logger } from '@nestjs/common'
import { Location, LocationType } from '@shared/types/Location'
import { LocationGenerationParams } from './types/world-generation.types'
import { AIHelper } from './ai.helper'

/**
 * Генератор локаций
 * Создает локации на основе параметров мира и биомов
 */
@Injectable()
export class LocationGenerator {
  private readonly logger = new Logger(LocationGenerator.name)
  
  constructor(private readonly aiHelper: AIHelper) {}

  /**
   * Генерирует локации для мира
   */
  async generateLocations(params: LocationGenerationParams): Promise<Location[]> {
    this.logger.log('Starting location generation...')
    
    const locations: Location[] = []
    const locationCount = this.calculateLocationCount(params)
    
    for (let i = 0; i < locationCount; i++) {
      try {
        const location = await this.generateSingleLocation(params, i, locations)
        locations.push(location)
      } catch (error) {
        this.logger.warn(`Failed to generate location ${i}: ${error.message}`)
      }
    }
    
    this.logger.log(`Generated ${locations.length} locations`)
    return locations
  }

  /**
   * Генерирует одну локацию
   */
  private async generateSingleLocation(
    params: LocationGenerationParams, 
    index: number, 
    existingLocations: Location[]
  ): Promise<Location> {
    
    // Определяем позицию локации
    const position = this.generateLocationPosition(params, existingLocations)
    
    // Определяем тип локации на основе биома и опасности
    const locationType = this.selectLocationType(params, position)
    
    // Генерируем базовые свойства
    const baseLocation = this.createBaseLocation(locationType, position, index)
    
    // Добавляем детали через AI (если включено)
    if (params.generateWithAI) {
      return await this.enhanceLocationWithAI(baseLocation, params)
    }
    
    return baseLocation
  }

  /**
   * Вычисляет количество локаций для генерации
   */
  private calculateLocationCount(params: LocationGenerationParams): number {
    const worldArea = params.worldSize.width * params.worldSize.height
    const baseDensity = 0.05 // 5% ячеек содержат локации
    const densityMultiplier = params.locationDensity / 100
    
    return Math.floor(worldArea * baseDensity * densityMultiplier)
  }

  /**
   * Генерирует позицию для новой локации
   */
  private generateLocationPosition(
    params: LocationGenerationParams, 
    existingLocations: Location[]
  ): { x: number; y: number } {
    
    const maxAttempts = 100
    let attempts = 0
    
    while (attempts < maxAttempts) {
      const x = Math.floor(Math.random() * params.worldSize.width)
      const y = Math.floor(Math.random() * params.worldSize.height)
      
      // Проверяем, что позиция не занята
      const isOccupied = existingLocations.some(loc => 
        loc.position.x === x && loc.position.y === y
      )
      
      if (!isOccupied) {
        return { x, y }
      }
      
      attempts++
    }
    
    // Если не удалось найти свободную позицию, используем случайную
    return {
      x: Math.floor(Math.random() * params.worldSize.width),
      y: Math.floor(Math.random() * params.worldSize.height)
    }
  }

  /**
   * Выбирает тип локации на основе параметров
   */
  private selectLocationType(
    params: LocationGenerationParams, 
    position: { x: number; y: number }
  ): LocationType {
    
    // Простая логика выбора типа на основе позиции и опасности
    const dangerLevel = params.dangerLevel
    const distanceFromCenter = this.calculateDistanceFromCenter(position, params.worldSize)
    
    // Центр мира - более безопасные локации
    if (distanceFromCenter < 0.3) {
      return this.selectSafeLocationType()
    }
    
    // Края мира - более опасные локации
    if (distanceFromCenter > 0.7) {
      return this.selectDangerousLocationType()
    }
    
    // Средняя зона - смешанные типы
    return this.selectMixedLocationType(dangerLevel)
  }

  /**
   * Вычисляет расстояние от центра мира (0-1)
   */
  private calculateDistanceFromCenter(
    position: { x: number; y: number }, 
    worldSize: { width: number; height: number }
  ): number {
    const centerX = worldSize.width / 2
    const centerY = worldSize.height / 2
    const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY)
    const distance = Math.sqrt(
      Math.pow(position.x - centerX, 2) + Math.pow(position.y - centerY, 2)
    )
    
    return distance / maxDistance
  }

  /**
   * Выбирает безопасный тип локации
   */
  private selectSafeLocationType(): LocationType {
    const safeTypes = [
      LocationType.SETTLEMENT,
      LocationType.SHELTER,
      LocationType.GARAGE,
      LocationType.SCHOOL
    ]
    
    return safeTypes[Math.floor(Math.random() * safeTypes.length)]
  }

  /**
   * Выбирает опасный тип локации
   */
  private selectDangerousLocationType(): LocationType {
    const dangerousTypes = [
      LocationType.MILITARY_BASE,
      LocationType.FACTORY,
      LocationType.RUINS,
      LocationType.CAVE
    ]
    
    return dangerousTypes[Math.floor(Math.random() * dangerousTypes.length)]
  }

  /**
   * Выбирает смешанный тип локации
   */
  private selectMixedLocationType(dangerLevel: number): LocationType {
    const allTypes = Object.values(LocationType)
    
    // Фильтруем типы на основе уровня опасности
    if (dangerLevel < 30) {
      return this.selectSafeLocationType()
    } else if (dangerLevel > 70) {
      return this.selectDangerousLocationType()
    }
    
    return allTypes[Math.floor(Math.random() * allTypes.length)]
  }

  /**
   * Создает базовую локацию
   */
  private createBaseLocation(
    type: LocationType, 
    position: { x: number; y: number }, 
    index: number
  ): Location {
    
    const locationId = `location_${index}_${position.x}_${position.y}`
    
    return {
      id: locationId,
      name: this.generateLocationName(type, index),
      description: this.generateLocationDescription(type),
      type: type,
      position: position,
      
      // Размеры локации
      size: {
        width: this.generateLocationSize(type),
        height: this.generateLocationSize(type)
      },
      
      // Базовые характеристики
      dangerLevel: this.calculateLocationDangerLevel(type),
      radiationLevel: this.calculateRadiationLevel(type),
      temperature: 20,
      lightLevel: 50,
      
      // Ресурсы и удобства
      hasWater: this.hasWater(type),
      hasElectricity: this.hasElectricity(type),
      hasShelter: this.hasShelter(type),
      
      // Безопасность
      safeZone: this.isSafeZone(type),
      enemySpawnRate: this.calculateEnemySpawnRate(type),
      
      // Инициализируем пустые массивы
      npcs: [],
      containers: [],
      lootTables: [],
      exits: [],
      connectedLocations: [],
      hazards: [],
      events: [],
      enemyTypes: [],
      
      // Быстрое перемещение
      canFastTravel: this.canFastTravel(type),
      
      // Ресурсы
      availableResources: this.generateAvailableResources(type),
      
      // Погодные эффекты
      weatherEffects: {
        visibility: 100,
        movementSpeed: 100,
        combatAccuracy: 100
      },
      
      // Статистика посещений
      visitCount: 0,
      totalTimeSpent: 0,
      
      // Временные метки
      createdAt: new Date(),
      lastUpdatedAt: new Date()
    }
  }

  /**
   * Генерирует название локации
   */
  private generateLocationName(type: LocationType, index: number): string {
    const typeNames = {
      [LocationType.BUNKER]: 'Бункер',
      [LocationType.SHELTER]: 'Убежище',
      [LocationType.GARAGE]: 'Гараж',
      [LocationType.WASTELAND]: 'Пустошь',
      [LocationType.RUINS]: 'Руины',
      [LocationType.SETTLEMENT]: 'Поселение',
      [LocationType.FACTORY]: 'Завод',
      [LocationType.HOSPITAL]: 'Больница',
      [LocationType.SCHOOL]: 'Школа',
      [LocationType.MILITARY_BASE]: 'Военная база',
      [LocationType.VAULT]: 'Хранилище',
      [LocationType.CAVE]: 'Пещера'
    }
    
    const baseName = typeNames[type] || 'Локация'
    return `${baseName} ${index + 1}`
  }

  /**
   * Генерирует описание локации
   */
  private generateLocationDescription(type: LocationType): string {
    const descriptions = {
      [LocationType.BUNKER]: 'Укрепленное подземное сооружение, построенное для защиты от ядерной войны.',
      [LocationType.SHELTER]: 'Временное убежище для выживших.',
      [LocationType.GARAGE]: 'Заброшенный гараж с остатками довоенной техники.',
      [LocationType.WASTELAND]: 'Бесплодная земля, покрытая радиоактивным пеплом.',
      [LocationType.RUINS]: 'Разрушенные остатки довоенного здания.',
      [LocationType.SETTLEMENT]: 'Небольшое поселение выживших.',
      [LocationType.FACTORY]: 'Заброшенный промышленный комплекс.',
      [LocationType.HOSPITAL]: 'Разрушенная больница с медицинскими припасами.',
      [LocationType.SCHOOL]: 'Заброшенная школа довоенного времени.',
      [LocationType.MILITARY_BASE]: 'Военная база с оружием и снаряжением.',
      [LocationType.VAULT]: 'Секретное хранилище ценностей.',
      [LocationType.CAVE]: 'Естественная пещера, служащая укрытием.'
    }
    
    return descriptions[type] || 'Неизвестная локация.'
  }

  // Вспомогательные методы для определения характеристик локации
  private generateLocationSize(type: LocationType): number {
    const sizes = {
      [LocationType.SETTLEMENT]: 20,
      [LocationType.MILITARY_BASE]: 25,
      [LocationType.FACTORY]: 30,
      [LocationType.HOSPITAL]: 15,
      [LocationType.BUNKER]: 10
    }
    
    return sizes[type] || 10
  }

  private calculateLocationDangerLevel(type: LocationType): number {
    const dangerLevels = {
      [LocationType.SETTLEMENT]: 10,
      [LocationType.SHELTER]: 5,
      [LocationType.MILITARY_BASE]: 80,
      [LocationType.FACTORY]: 60,
      [LocationType.RUINS]: 40
    }
    
    return dangerLevels[type] || 30
  }

  private calculateRadiationLevel(type: LocationType): number {
    const radiationLevels = {
      [LocationType.BUNKER]: 5,
      [LocationType.SHELTER]: 10,
      [LocationType.FACTORY]: 70,
      [LocationType.MILITARY_BASE]: 50
    }
    
    return radiationLevels[type] || 25
  }

  private hasWater(type: LocationType): boolean {
    return [LocationType.SETTLEMENT, LocationType.HOSPITAL, LocationType.BUNKER].includes(type)
  }

  private hasElectricity(type: LocationType): boolean {
    return [LocationType.BUNKER, LocationType.MILITARY_BASE, LocationType.FACTORY].includes(type)
  }

  private hasShelter(type: LocationType): boolean {
    return type !== LocationType.WASTELAND
  }

  private isSafeZone(type: LocationType): boolean {
    return [LocationType.SETTLEMENT, LocationType.SHELTER, LocationType.BUNKER].includes(type)
  }

  private calculateEnemySpawnRate(type: LocationType): number {
    const spawnRates = {
      [LocationType.SETTLEMENT]: 0,
      [LocationType.SHELTER]: 5,
      [LocationType.MILITARY_BASE]: 60,
      [LocationType.RUINS]: 40
    }
    
    return spawnRates[type] || 20
  }

  private canFastTravel(type: LocationType): boolean {
    return [LocationType.SETTLEMENT, LocationType.BUNKER].includes(type)
  }

  private generateAvailableResources(type: LocationType): any[] {
    // Упрощенная генерация ресурсов
    return []
  }

  /**
   * Улучшает локацию с помощью AI
   */
  private async enhanceLocationWithAI(location: Location, params: LocationGenerationParams): Promise<Location> {
    try {
      // Здесь будет вызов AI для улучшения описания и добавления деталей
      // Пока возвращаем базовую локацию
      return location
    } catch (error) {
      this.logger.warn(`Failed to enhance location with AI: ${error.message}`)
      return location
    }
  }
}
