import { Injectable, Logger } from '@nestjs/common'
import { MapCell, TerrainType } from '@shared/types/MapCell'
import { MapGenerationParams } from './types/world-generation.types'

/**
 * Генератор карты мира
 * Создает сетку MapCell[][] с различными алгоритмами генерации
 */
@Injectable()
export class MapGenerator {
  private readonly logger = new Logger(MapGenerator.name)

  /**
   * Генерирует карту мира
   */
  async generateMap(params: MapGenerationParams): Promise<MapCell[][]> {
    this.logger.log(`Generating map ${params.width}x${params.height} with algorithm: ${params.algorithm}`)
    
    // Инициализируем сетку
    const grid = this.initializeGrid(params.width, params.height)
    
    // Применяем алгоритм генерации
    switch (params.algorithm) {
      case 'perlin':
        this.generateWithPerlinNoise(grid, params)
        break
      case 'cellular':
        this.generateWithCellularAutomata(grid, params)
        break
      case 'voronoi':
        this.generateWithVoronoi(grid, params)
        break
      case 'hybrid':
        this.generateWithHybrid(grid, params)
        break
      default:
        this.generateWithSimple(grid, params)
    }
    
    // Пост-обработка
    this.postProcessMap(grid, params)
    
    this.logger.log('Map generation completed')
    return grid
  }

  /**
   * Инициализирует пустую сетку
   */
  private initializeGrid(width: number, height: number): MapCell[][] {
    const grid: MapCell[][] = []
    
    for (let y = 0; y < height; y++) {
      grid[y] = []
      for (let x = 0; x < width; x++) {
        grid[y][x] = this.createBaseCell(x, y)
      }
    }
    
    return grid
  }

  /**
   * Создает базовую ячейку карты
   */
  private createBaseCell(x: number, y: number): MapCell {
    return {
      coord: { x, y },
      terrain: TerrainType.WASTELAND,
      elevation: 0,
      temperature: 20,
      radiationLevel: 10,
      
      isVisible: false,
      isExplored: false,
      fogOfWar: true,
      
      hasLocation: false,
      structures: [],
      
      isPassable: true,
      movementCost: 1,
      
      resources: [],
      hasLoot: false,
      isLooted: false,
      
      weather: {
        current: 'clear',
        visibility: 100,
        temperature: 20,
        windSpeed: 5
      },
      
      dangers: [],
      
      biome: 'wasteland',
      
      lastUpdatedAt: new Date()
    }
  }

  /**
   * Генерация с помощью шума Перлина (упрощенная версия)
   */
  private generateWithPerlinNoise(grid: MapCell[][], params: MapGenerationParams): void {
    const { width, height, settings } = params
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const cell = grid[y][x]
        
        // Упрощенный шум Перлина
        const noiseValue = this.simpleNoise(x * 0.1, y * 0.1, params.seed)
        
        // Определяем тип местности на основе шума
        if (noiseValue > 0.6) {
          cell.terrain = TerrainType.MOUNTAIN
          cell.elevation = Math.floor((noiseValue - 0.6) * 100)
          cell.movementCost = 3
        } else if (noiseValue > 0.3) {
          cell.terrain = TerrainType.HILLS
          cell.elevation = Math.floor((noiseValue - 0.3) * 50)
          cell.movementCost = 2
        } else if (noiseValue > 0.1) {
          cell.terrain = TerrainType.PLAINS
          cell.elevation = 0
          cell.movementCost = 1
        } else {
          cell.terrain = TerrainType.WATER
          cell.elevation = -10
          cell.movementCost = 4
          cell.isPassable = false
        }
        
        // Устанавливаем радиацию
        cell.radiationLevel = settings.globalRadiation + Math.floor(Math.random() * 20)
        
        // Температура зависит от высоты
        cell.temperature = 20 - (cell.elevation * 0.1)
      }
    }
  }

  /**
   * Генерация с помощью клеточных автоматов
   */
  private generateWithCellularAutomata(grid: MapCell[][], params: MapGenerationParams): void {
    const { width, height } = params
    
    // Инициализируем случайными значениями
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const cell = grid[y][x]
        const random = this.seededRandom(params.seed + x + y * width)
        
        if (random > 0.55) {
          cell.terrain = TerrainType.MOUNTAIN
          cell.isPassable = true
          cell.movementCost = 3
        } else {
          cell.terrain = TerrainType.PLAINS
          cell.isPassable = true
          cell.movementCost = 1
        }
      }
    }
    
    // Применяем несколько итераций клеточного автомата
    for (let iteration = 0; iteration < 5; iteration++) {
      this.applyCellularAutomataRules(grid, width, height)
    }
  }

  /**
   * Применяет правила клеточного автомата
   */
  private applyCellularAutomataRules(grid: MapCell[][], width: number, height: number): void {
    const newGrid = grid.map(row => row.map(cell => ({ ...cell })))
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const mountainNeighbors = this.countMountainNeighbors(grid, x, y)
        
        if (mountainNeighbors >= 4) {
          newGrid[y][x].terrain = TerrainType.MOUNTAIN
          newGrid[y][x].movementCost = 3
        } else if (mountainNeighbors <= 2) {
          newGrid[y][x].terrain = TerrainType.PLAINS
          newGrid[y][x].movementCost = 1
        }
      }
    }
    
    // Копируем результат обратно
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        grid[y][x] = newGrid[y][x]
      }
    }
  }

  /**
   * Подсчитывает соседей-гор
   */
  private countMountainNeighbors(grid: MapCell[][], x: number, y: number): number {
    let count = 0
    
    for (let dy = -1; dy <= 1; dy++) {
      for (let dx = -1; dx <= 1; dx++) {
        if (dx === 0 && dy === 0) continue
        
        const nx = x + dx
        const ny = y + dy
        
        if (nx >= 0 && nx < grid[0].length && ny >= 0 && ny < grid.length) {
          if (grid[ny][nx].terrain === TerrainType.MOUNTAIN) {
            count++
          }
        }
      }
    }
    
    return count
  }

  /**
   * Генерация с помощью диаграммы Вороного (упрощенная)
   */
  private generateWithVoronoi(grid: MapCell[][], params: MapGenerationParams): void {
    const { width, height } = params
    const seedPoints = this.generateVoronoiSeeds(width, height, 20)
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const cell = grid[y][x]
        const closestSeed = this.findClosestSeed(x, y, seedPoints)
        
        // Устанавливаем тип местности на основе ближайшего семени
        cell.terrain = closestSeed.terrain
        cell.movementCost = this.getMovementCostForTerrain(closestSeed.terrain)
        cell.isPassable = closestSeed.terrain !== TerrainType.WATER
        
        // Расстояние до семени влияет на характеристики
        const distance = Math.sqrt(Math.pow(x - closestSeed.x, 2) + Math.pow(y - closestSeed.y, 2))
        cell.elevation = Math.max(0, closestSeed.elevation - distance * 2)
      }
    }
  }

  /**
   * Генерирует семена для диаграммы Вороного
   */
  private generateVoronoiSeeds(width: number, height: number, count: number): any[] {
    const seeds = []
    const terrainTypes = [TerrainType.PLAINS, TerrainType.HILLS, TerrainType.MOUNTAIN, TerrainType.WATER]
    
    for (let i = 0; i < count; i++) {
      seeds.push({
        x: Math.floor(Math.random() * width),
        y: Math.floor(Math.random() * height),
        terrain: terrainTypes[Math.floor(Math.random() * terrainTypes.length)],
        elevation: Math.floor(Math.random() * 50)
      })
    }
    
    return seeds
  }

  /**
   * Находит ближайшее семя
   */
  private findClosestSeed(x: number, y: number, seeds: any[]): any {
    let closest = seeds[0]
    let minDistance = Infinity
    
    for (const seed of seeds) {
      const distance = Math.sqrt(Math.pow(x - seed.x, 2) + Math.pow(y - seed.y, 2))
      if (distance < minDistance) {
        minDistance = distance
        closest = seed
      }
    }
    
    return closest
  }

  /**
   * Гибридная генерация (комбинация алгоритмов)
   */
  private generateWithHybrid(grid: MapCell[][], params: MapGenerationParams): void {
    // Сначала применяем шум Перлина для базовой формы
    this.generateWithPerlinNoise(grid, params)
    
    // Затем применяем клеточные автоматы для сглаживания
    this.applyCellularAutomataRules(grid, params.width, params.height)
    
    // Добавляем водоемы с помощью Вороного
    this.addWaterBodies(grid, params)
  }

  /**
   * Простая генерация (случайная)
   */
  private generateWithSimple(grid: MapCell[][], params: MapGenerationParams): void {
    const { width, height, settings } = params
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const cell = grid[y][x]
        const random = this.seededRandom(params.seed + x + y * width)
        
        if (random > 0.8) {
          cell.terrain = TerrainType.MOUNTAIN
          cell.movementCost = 3
        } else if (random > 0.6) {
          cell.terrain = TerrainType.HILLS
          cell.movementCost = 2
        } else if (random > 0.1) {
          cell.terrain = TerrainType.PLAINS
          cell.movementCost = 1
        } else {
          cell.terrain = TerrainType.WATER
          cell.movementCost = 4
          cell.isPassable = false
        }
      }
    }
  }

  /**
   * Добавляет водоемы
   */
  private addWaterBodies(grid: MapCell[][], params: MapGenerationParams): void {
    const { width, height } = params
    const waterSources = 3 + Math.floor(Math.random() * 3) // 3-5 водоемов
    
    for (let i = 0; i < waterSources; i++) {
      const centerX = Math.floor(Math.random() * width)
      const centerY = Math.floor(Math.random() * height)
      const radius = 2 + Math.floor(Math.random() * 4) // радиус 2-5
      
      this.createWaterBody(grid, centerX, centerY, radius, width, height)
    }
  }

  /**
   * Создает водоем
   */
  private createWaterBody(grid: MapCell[][], centerX: number, centerY: number, radius: number, width: number, height: number): void {
    for (let y = Math.max(0, centerY - radius); y <= Math.min(height - 1, centerY + radius); y++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(width - 1, centerX + radius); x++) {
        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
        
        if (distance <= radius) {
          grid[y][x].terrain = TerrainType.WATER
          grid[y][x].isPassable = false
          grid[y][x].movementCost = 4
          grid[y][x].elevation = -5
        }
      }
    }
  }

  /**
   * Пост-обработка карты
   */
  private postProcessMap(grid: MapCell[][], params: MapGenerationParams): void {
    this.addRadiationZones(grid, params)
    this.addResources(grid, params)
    this.setBiomes(grid, params)
  }

  /**
   * Добавляет зоны радиации
   */
  private addRadiationZones(grid: MapCell[][], params: MapGenerationParams): void {
    if (!params.settings.radiationZones) return
    
    const { width, height } = params
    const zoneCount = 2 + Math.floor(Math.random() * 3) // 2-4 зоны
    
    for (let i = 0; i < zoneCount; i++) {
      const centerX = Math.floor(Math.random() * width)
      const centerY = Math.floor(Math.random() * height)
      const radius = 3 + Math.floor(Math.random() * 5) // радиус 3-7
      const intensity = 50 + Math.floor(Math.random() * 50) // 50-100 рад
      
      this.createRadiationZone(grid, centerX, centerY, radius, intensity, width, height)
    }
  }

  /**
   * Создает зону радиации
   */
  private createRadiationZone(grid: MapCell[][], centerX: number, centerY: number, radius: number, intensity: number, width: number, height: number): void {
    for (let y = Math.max(0, centerY - radius); y <= Math.min(height - 1, centerY + radius); y++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(width - 1, centerX + radius); x++) {
        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
        
        if (distance <= radius) {
          const falloff = 1 - (distance / radius)
          grid[y][x].radiationLevel += Math.floor(intensity * falloff)
        }
      }
    }
  }

  /**
   * Добавляет ресурсы
   */
  private addResources(grid: MapCell[][], params: MapGenerationParams): void {
    const { width, height } = params
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const cell = grid[y][x]
        
        // Вероятность ресурсов зависит от типа местности
        const resourceChance = this.getResourceChance(cell.terrain)
        
        if (Math.random() < resourceChance) {
          cell.hasLoot = true
          cell.resources.push({
            type: this.selectResourceType(cell.terrain),
            amount: Math.floor(Math.random() * 10) + 1,
            quality: Math.floor(Math.random() * 100)
          })
        }
      }
    }
  }

  /**
   * Устанавливает биомы
   */
  private setBiomes(grid: MapCell[][], params: MapGenerationParams): void {
    const { width, height } = params
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const cell = grid[y][x]
        cell.biome = this.determineBiome(cell, x, y, width, height)
      }
    }
  }

  /**
   * Определяет биом ячейки
   */
  private determineBiome(cell: MapCell, x: number, y: number, width: number, height: number): string {
    // Простая логика определения биома
    if (cell.terrain === TerrainType.WATER) return 'water'
    if (cell.terrain === TerrainType.MOUNTAIN) return 'mountain'
    if (cell.radiationLevel > 50) return 'irradiated_wasteland'
    
    // Биом зависит от позиции на карте
    const distanceFromCenter = Math.sqrt(Math.pow(x - width/2, 2) + Math.pow(y - height/2, 2))
    const maxDistance = Math.sqrt(Math.pow(width/2, 2) + Math.pow(height/2, 2))
    const normalizedDistance = distanceFromCenter / maxDistance
    
    if (normalizedDistance < 0.3) return 'urban_ruins'
    if (normalizedDistance < 0.6) return 'wasteland'
    return 'wild_wasteland'
  }

  // Вспомогательные методы
  private simpleNoise(x: number, y: number, seed: string): number {
    // Упрощенная функция шума
    const hash = this.hashString(seed + x.toString() + y.toString())
    return (hash % 1000) / 1000
  }

  private seededRandom(seed: string | number): number {
    const hash = typeof seed === 'string' ? this.hashString(seed) : seed
    return (hash % 1000) / 1000
  }

  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32bit integer
    }
    return Math.abs(hash)
  }

  private getMovementCostForTerrain(terrain: TerrainType): number {
    const costs = {
      [TerrainType.PLAINS]: 1,
      [TerrainType.HILLS]: 2,
      [TerrainType.MOUNTAIN]: 3,
      [TerrainType.WATER]: 4,
      [TerrainType.WASTELAND]: 1
    }
    return costs[terrain] || 1
  }

  private getResourceChance(terrain: TerrainType): number {
    const chances = {
      [TerrainType.MOUNTAIN]: 0.3,
      [TerrainType.HILLS]: 0.2,
      [TerrainType.PLAINS]: 0.1,
      [TerrainType.WASTELAND]: 0.15,
      [TerrainType.WATER]: 0.05
    }
    return chances[terrain] || 0.1
  }

  private selectResourceType(terrain: TerrainType): string {
    const resourceTypes = {
      [TerrainType.MOUNTAIN]: ['metal', 'stone', 'rare_minerals'],
      [TerrainType.HILLS]: ['metal', 'stone'],
      [TerrainType.PLAINS]: ['food', 'wood'],
      [TerrainType.WASTELAND]: ['scrap', 'chemicals'],
      [TerrainType.WATER]: ['water', 'fish']
    }
    
    const types = resourceTypes[terrain] || ['scrap']
    return types[Math.floor(Math.random() * types.length)]
  }
}
