import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

export interface AIRequest {
  prompt: string;
  context?: any;
  max_length?: number;
  temperature?: number;
}

export interface AIResponse {
  success: boolean;
  content: string;
  provider_used?: string;
  error?: string;
}

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);
  private readonly aiServiceUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.aiServiceUrl = this.configService.get<string>('AI_SERVICE_URL') || 'http://localhost:3005';
  }

  async generateStory(request: AIRequest): Promise<AIResponse> {
    try {
      this.logger.log('Generating story with AI service');

      const response = await firstValueFrom(
        this.httpService.post(`${this.aiServiceUrl}/generate/story`, request)
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to generate story', error.message);
      return {
        success: false,
        content: '',
        error: 'Failed to generate story content'
      };
    }
  }

  async generateQuest(questType: string, playerLevel: number, location: string, context?: any): Promise<AIResponse> {
    try {
      this.logger.log('Generating quest with AI service');

      const response = await firstValueFrom(
        this.httpService.post(`${this.aiServiceUrl}/generate/quest`, {
          quest_type: questType,
          player_level: playerLevel,
          location,
          context
        })
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to generate quest', error.message);
      return {
        success: false,
        content: '',
        error: 'Failed to generate quest content'
      };
    }
  }

  async generateDialogue(characterName: string, characterRole: string, personality: string, context: any, playerMessage?: string): Promise<AIResponse> {
    try {
      this.logger.log('Generating dialogue with AI service');

      const response = await firstValueFrom(
        this.httpService.post(`${this.aiServiceUrl}/generate/dialogue`, {
          character_name: characterName,
          character_role: characterRole,
          personality,
          context,
          player_message: playerMessage
        })
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to generate dialogue', error.message);
      return {
        success: false,
        content: '',
        error: 'Failed to generate dialogue content'
      };
    }
  }

  async generateEvent(eventType: string, context?: any): Promise<AIResponse> {
    try {
      this.logger.log('Generating event with AI service');

      const response = await firstValueFrom(
        this.httpService.post(`${this.aiServiceUrl}/generate/event`, {
          event_type: eventType,
          context
        })
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to generate event', error.message);
      return {
        success: false,
        content: '',
        error: 'Failed to generate event content'
      };
    }
  }
}
