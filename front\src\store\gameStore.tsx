import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

export interface World {
  id: string
  name: string
  createdAt: Date
  lastPlayed: Date
  progress: number
  description?: string
}

export interface GameSettings {
  volume: number
  soundEnabled: boolean
  musicEnabled: boolean
  difficulty: 'easy' | 'normal' | 'hard'
  autoSave: boolean
}

interface GameState {
  worlds: World[]
  settings: GameSettings
  currentWorldId: string | null
  
  // Actions
  addWorld: (world: Omit<World, 'id' | 'createdAt' | 'lastPlayed'>) => void
  deleteWorld: (worldId: string) => void
  updateWorld: (worldId: string, updates: Partial<World>) => void
  setCurrentWorld: (worldId: string | null) => void
  updateSettings: (settings: Partial<GameSettings>) => void
  loadWorld: (worldId: string) => void
}

const defaultSettings: GameSettings = {
  volume: 0.7,
  soundEnabled: true,
  musicEnabled: true,
  difficulty: 'normal',
  autoSave: true
}

export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      worlds: [],
      settings: defaultSettings,
      currentWorldId: null,

      addWorld: (worldData) => {
        const newWorld: World = {
          ...worldData,
          id: crypto.randomUUID(),
          createdAt: new Date(),
          lastPlayed: new Date(),
          progress: 0
        }
        
        set((state) => ({
          worlds: [...state.worlds, newWorld]
        }))
      },

      deleteWorld: (worldId) => {
        set((state) => ({
          worlds: state.worlds.filter(world => world.id !== worldId),
          currentWorldId: state.currentWorldId === worldId ? null : state.currentWorldId
        }))
      },

      updateWorld: (worldId, updates) => {
        set((state) => ({
          worlds: state.worlds.map(world =>
            world.id === worldId
              ? { ...world, ...updates, lastPlayed: new Date() }
              : world
          )
        }))
      },

      setCurrentWorld: (worldId) => {
        set({ currentWorldId: worldId })
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      loadWorld: (worldId) => {
        const { updateWorld, setCurrentWorld } = get()
        updateWorld(worldId, { lastPlayed: new Date() })
        setCurrentWorld(worldId)
      }
    }),
    {
      name: 'nuclear-story-game-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        worlds: state.worlds,
        settings: state.settings,
        currentWorldId: state.currentWorldId
      })
    }
  )
)
