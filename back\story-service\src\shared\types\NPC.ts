export enum NPCType {
  TRADER = 'trader',
  GUARD = 'guard',
  SCIENTIST = 'scientist',
  RAIDER = 'raider',
  SURVIVOR = 'survivor',
  MUTANT = 'mutant',
  ROBOT = 'robot'
}

export interface NPC {
  id: string
  name: string
  type: NPCType
  description: string
  currentLocationId: string
  factionId?: string
  level: number
  health: number
  maxHealth: number
  stats: {
    strength: number
    agility: number
    intelligence: number
    charisma: number
  }
  inventory: Record<string, number>
  isHostile: boolean
  isTrader: boolean
  dialogue?: string[]
}
