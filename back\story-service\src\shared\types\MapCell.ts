export enum TerrainType {
  WASTELAND = 'wasteland',
  RUINS = 'ruins',
  FOREST = 'forest',
  DESERT = 'desert',
  MOUNTAINS = 'mountains',
  WATER = 'water',
  RADIATION = 'radiation',
  SETTLEMENT = 'settlement'
}

export interface MapCell {
  x: number
  y: number
  terrain: TerrainType
  elevation: number
  radiationLevel: number
  resources: Record<string, number>
  locationId?: string
  isExplored: boolean
  isVisible: boolean
}
